# =============================================================================
# 🔧 AUDIT SUMMARY MICROSERVICE - ENVIRONMENT CONFIGURATION
# =============================================================================
# Template de configuração para o Audit Summary Microservice
# Copie este arquivo para .env e configure os valores apropriados
# 
# IMPORTANTE: Nunca commite o arquivo .env com credenciais reais!
# =============================================================================

# =============================================================================
# 🗄️ DATABASE CONFIGURATION
# =============================================================================

# MySQL Database - Primary Connection
# RDS endpoint para conexão com banco principal
DB_HOST=audta-ai-instance.cdkyomc4a6x4.us-east-1.rds.amazonaws.com
DB_PORT=3306
DB_USER=devdb
DB_PASSWORD=your_secure_password_here

# Database Names
# audit_landzone: Dados brutos de conversas e atributos
DB_LANDZONE=audit_landzone
# audit_gold: Resultados processados de auditoria
DB_GOLD=audit_gold

# Connection Pool Settings (opcional)
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30

# =============================================================================
# 🤖 AI SERVICES CONFIGURATION  
# =============================================================================

# OpenAI Configuration
# Obter em: https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_ORG_ID=org-your-organization-id  # Opcional

# Anthropic Claude Configuration  
# Obter em: https://console.anthropic.com/
ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key-here

# DeepSeek Configuration (opcional)
# Para modelos alternativos de baixo custo
DEEPSEEK_API_KEY=sk-your-deepseek-api-key-here

# Primary AI Model Selection
# Opções: claude-3-5-sonnet-20241022, gpt-4, gpt-3.5-turbo
MODEL_NAME=claude-3-5-sonnet-20241022

# Model-specific configurations
CLAUDE_MAX_TOKENS=10000
CLAUDE_TEMPERATURE=0
CLAUDE_TOP_P=0

GPT_MAX_TOKENS=8000
GPT_TEMPERATURE=0.1
GPT_TOP_P=0.9

# =============================================================================
# ☁️ AWS SERVICES CONFIGURATION
# =============================================================================

# AWS Credentials
# Para acesso a RDS, Secrets Manager, Bedrock
AWS_ACCESS_KEY_ID=AKIA...your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-access-key
AWS_REGION_NAME=us-east-1

# AWS Secrets Manager
# Nome do secret que contém credenciais sensíveis
AWS_SECRET_NAME=prod/audie

# AWS Bedrock Configuration (opcional)
# Para usar modelos Llama via Bedrock
BEDROCK_MODEL_ID=arn:aws:bedrock:us-east-1:603651401763:imported-model/ta1av12mu9bp
BEDROCK_REGION=us-east-1

# =============================================================================
# 🚀 APPLICATION CONFIGURATION
# =============================================================================

# Environment
# Valores: development, staging, production
ENVIRONMENT=development

# Logging Configuration
LOG_LEVEL=INFO
# Valores: DEBUG, INFO, WARNING, ERROR, CRITICAL

# Enable JSON structured logging
JSON_LOGGING=true

# Request ID tracking
ENABLE_REQUEST_ID=true

# =============================================================================
# 🌐 API CONFIGURATION
# =============================================================================

# FastAPI Settings
API_HOST=0.0.0.0
API_PORT=8000
API_RELOAD=true  # Apenas para development

# CORS Configuration
# Para production, especificar origins exatos
CORS_ORIGINS=["*"]  # ⚠️ Alterar para production
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=["GET", "POST", "PUT", "DELETE"]
CORS_ALLOW_HEADERS=["*"]

# API Rate Limiting (opcional)
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60  # segundos

# =============================================================================
# 🔒 SECURITY CONFIGURATION
# =============================================================================

# JWT Configuration (se implementado)
JWT_SECRET_KEY=your-super-secret-jwt-key-here
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# API Key Authentication (opcional)
API_KEY=your-api-key-for-service-authentication

# Encryption (para dados sensíveis)
ENCRYPTION_KEY=your-32-byte-encryption-key-here

# =============================================================================
# 📊 MONITORING & OBSERVABILITY
# =============================================================================

# Performance Monitoring
ENABLE_PERFORMANCE_MONITORING=true
SLOW_QUERY_THRESHOLD=1.0  # segundos

# Health Check Configuration
HEALTH_CHECK_TIMEOUT=30
HEALTH_CHECK_INTERVAL=60

# Metrics Collection
ENABLE_METRICS=true
METRICS_PORT=9090

# =============================================================================
# 🧪 TESTING CONFIGURATION
# =============================================================================

# Test Database (opcional)
TEST_DB_HOST=localhost
TEST_DB_NAME=audit_test
TEST_DB_USER=test_user
TEST_DB_PASSWORD=test_password

# Mock AI Responses for Testing
MOCK_AI_RESPONSES=false
TEST_MODE=false

# =============================================================================
# 🐳 DOCKER CONFIGURATION
# =============================================================================

# Container Settings
CONTAINER_PORT=8000
CONTAINER_WORKERS=4

# Docker Compose overrides
COMPOSE_PROJECT_NAME=audit-summary-ms

# =============================================================================
# 📓 JUPYTER NOTEBOOK CONFIGURATION
# =============================================================================

# Jupyter Settings para análise
JUPYTER_PORT=8888
JUPYTER_TOKEN=your-jupyter-token-here
JUPYTER_ENABLE_EXTENSIONS=true

# Notebook execution
PAPERMILL_OUTPUT_DIR=/outputs
NOTEBOOK_TIMEOUT=3600  # segundos

# =============================================================================
# 🔧 DEVELOPMENT TOOLS
# =============================================================================

# Debug Mode
DEBUG=true  # Apenas para development
VERBOSE_LOGGING=false

# Development Database (opcional)
DEV_DB_HOST=localhost
DEV_DB_PORT=3306

# Hot Reload
AUTO_RELOAD=true

# =============================================================================
# 📈 PERFORMANCE TUNING
# =============================================================================

# Database Query Optimization
QUERY_TIMEOUT=30
ENABLE_QUERY_CACHE=true
CACHE_TTL=300  # segundos

# AI Model Optimization
AI_REQUEST_TIMEOUT=60
AI_RETRY_ATTEMPTS=3
AI_RETRY_DELAY=1  # segundos

# Async Processing
MAX_CONCURRENT_REQUESTS=10
THREAD_POOL_SIZE=4

# =============================================================================
# 🚨 ALERTING & NOTIFICATIONS
# =============================================================================

# Slack Notifications (opcional)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/webhook/url
SLACK_CHANNEL=#alerts

# Email Notifications (opcional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-email-password

# =============================================================================
# 📝 NOTES & WARNINGS
# =============================================================================

# ⚠️ SECURITY WARNINGS:
# 1. Nunca commite este arquivo com credenciais reais
# 2. Use AWS Secrets Manager para credenciais em produção
# 3. Rotacione API keys regularmente
# 4. Configure CORS adequadamente para produção
# 5. Use HTTPS em produção

# 🔧 DEVELOPMENT NOTES:
# 1. Para desenvolvimento local, use valores de desenvolvimento
# 2. Para testes, configure variáveis TEST_*
# 3. Para produção, use valores seguros e validados

# 📚 DOCUMENTATION:
# - README.md: Documentação principal
# - docs/PYCHARM_SETUP.md: Configuração do PyCharm
# - docs/AGENT_CONTEXT.md: Contexto para agentes IA

# =============================================================================
# END OF CONFIGURATION
# =============================================================================
