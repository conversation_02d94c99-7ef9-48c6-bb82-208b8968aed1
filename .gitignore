# =============================================================================
# 🚫 GITIGNORE - AUDIT SUMMARY MICROSERVICE
# =============================================================================
# Arquivo .gitignore completo para projeto Python FastAPI com IA e AWS
# Evita commit de arquivos sensíveis, temporários e desnecessários
# =============================================================================

# =============================================================================
# 🔒 ENVIRONMENT & SECRETS
# =============================================================================

# Environment variables e configurações sensíveis
.env
.env.local
.env.development
.env.staging  
.env.production
.env.test

# AWS credentials
.aws/
aws-credentials.json
credentials.json

# API Keys e tokens
api-keys.txt
tokens.json
secrets.json

# Certificados e chaves
*.pem
*.key
*.crt
*.p12
*.pfx

# =============================================================================
# 🐍 PYTHON
# =============================================================================

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff (se aplicável)
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff (se aplicável)
instance/
.webassets-cache

# Scrapy stuff (se aplicável)
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints
*/.ipynb_checkpoints/*

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
teste/
.python-version

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# =============================================================================
# 🧠 AI & MACHINE LEARNING
# =============================================================================

# Model files
*.pkl
*.pickle
*.joblib
*.h5
*.hdf5
*.pb
*.onnx
*.tflite
*.pt
*.pth
*.bin
*.safetensors

# Training data
data/
datasets/
training_data/
test_data/
*.csv
*.json
*.parquet
*.feather

# Model outputs
outputs/
results/
predictions/
checkpoints/
logs/
runs/
wandb/
mlruns/

# Jupyter notebook outputs
*.ipynb
!Auditoria_criacao_resumos.ipynb  # Manter notebook principal

# TensorBoard logs
tensorboard_logs/

# =============================================================================
# 🗄️ DATABASE
# =============================================================================

# Database files
*.db
*.sqlite
*.sqlite3
*.db-journal

# Database dumps
*.sql
*.dump
*.backup

# Database migration files (se aplicável)
migrations/versions/
alembic/versions/

# =============================================================================
# 🐳 DOCKER
# =============================================================================

# Docker files
.dockerignore
docker-compose.override.yml
docker-compose.local.yml
docker-compose.prod.yml

# Docker volumes
docker-volumes/
.docker/

# =============================================================================
# ☁️ CLOUD & DEPLOYMENT
# =============================================================================

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl
terraform.tfvars
terraform.tfvars.json

# Kubernetes
*.kubeconfig
k8s-secrets.yaml

# Serverless Framework
.serverless/
serverless.yml.bak

# AWS SAM
.aws-sam/
samconfig.toml

# =============================================================================
# 💻 IDEs & EDITORS
# =============================================================================

# PyCharm
.idea/
*.iws
*.iml
*.ipr

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# 🖥️ OPERATING SYSTEMS
# =============================================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# 📝 LOGS & TEMPORARY FILES
# =============================================================================

# Log files
*.log
*.log.*
logs/
log/
*.out
*.err

# Temporary files
tmp/
temp/
.tmp/
*.tmp
*.temp
*.bak
*.backup
*.old
*.orig
*.save

# Cache files
.cache/
cache/
*.cache

# =============================================================================
# 📊 MONITORING & METRICS
# =============================================================================

# Prometheus
prometheus_data/

# Grafana
grafana_data/

# Application metrics
metrics/
monitoring/

# =============================================================================
# 🧪 TESTING
# =============================================================================

# Test outputs
test-results/
test-reports/
.coverage
htmlcov/
.pytest_cache/

# Performance test results
locust_reports/
jmeter_results/

# =============================================================================
# 📦 PACKAGE MANAGERS
# =============================================================================

# npm (se houver frontend)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn/

# =============================================================================
# 🔧 BUILD & DEPLOYMENT
# =============================================================================

# Build artifacts
build/
dist/
*.egg-info/

# Deployment files
deploy/
deployment/
.deploy/

# CI/CD
.github/workflows/secrets/
.gitlab-ci-local/

# =============================================================================
# 📚 DOCUMENTATION
# =============================================================================

# Generated documentation
docs/_build/
docs/build/
site/

# =============================================================================
# 🎯 PROJECT SPECIFIC
# =============================================================================

# Jupyter notebook outputs (manter estrutura, remover outputs)
*/.ipynb_checkpoints/

# AI model cache
.ai_cache/
model_cache/

# Database connection test files
test_connection.py
db_test.py

# Temporary analysis files
analysis_temp/
temp_analysis/

# Local configuration overrides
local_config.py
local_settings.py

# =============================================================================
# END OF GITIGNORE
# =============================================================================
