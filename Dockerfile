FROM python:3.11-slim

# Evita prompts e reduz tamanho
ENV PIP_NO_CACHE_DIR=1 PYTHONDONTWRITEBYTECODE=1 PYTHONUNBUFFERED=1

WORKDIR /app

# Se existir requirements, instale (não falha se não houver)
COPY requirements.txt /app/requirements.txt
RUN if [ -f requirements.txt ]; then pip install -r requirements.txt; fi

# Ferramentas para rodar/transformar notebooks (opcional)
RUN pip install jupyter papermill nbconvert

# Copia notebooks para a imagem
COPY ./*.ipynb /app/

# Nome do notebook principal pode ser alterado em build-time
ARG NB_FILE=Auditoria_criacao_resumos.ipynb
ENV NB_FILE=$NB_FILE

# Entrada padrão: executar o notebook com papermill e gerar HTML
# Você pode sobrescrever CMD em "docker run" se quiser outro comportamento
RUN mkdir -p /outputs
CMD bash -lc 'papermill "$NB_FILE" /outputs/run.ipynb && jupyter nbconvert --to html /outputs/run.ipynb --output /outputs/run.html && echo "Executado: /outputs/run.ipynb e /outputs/run.html" && tail -f /dev/null'
