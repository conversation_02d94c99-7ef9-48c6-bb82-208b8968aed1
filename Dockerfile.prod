# =============================================================================
# 🐳 PRODUCTION DOCKERFILE - AUDIT SUMMARY MICROSERVICE
# =============================================================================
# Multi-stage build otimizado para produção
# Baseado em Python 3.11 slim para menor footprint
# =============================================================================

# =============================================================================
# 📦 STAGE 1: BUILDER - Compilação e dependências
# =============================================================================
FROM python:3.11-slim as builder

# Metadados da imagem
LABEL maintainer="Rafael Carvalho Ferreira"
LABEL version="1.0"
LABEL description="Audit Summary Microservice - Production Build"

# Variáveis de build
ARG BUILD_DATE
ARG VCS_REF
ARG VERSION=1.0

# Labels para metadados
LABEL org.label-schema.build-date=$BUILD_DATE \
      org.label-schema.name="audit-summary-ms" \
      org.label-schema.description="AI-powered audit summary microservice" \
      org.label-schema.vcs-ref=$VCS_REF \
      org.label-schema.version=$VERSION \
      org.label-schema.schema-version="1.0"

# Configurações de otimização Python
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Instalar dependências do sistema necessárias para build
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    g++ \
    libc6-dev \
    libffi-dev \
    libssl-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Criar ambiente virtual
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Atualizar pip e instalar wheel
RUN pip install --upgrade pip setuptools wheel

# Copiar requirements e instalar dependências
COPY requirements.txt /tmp/requirements.txt
RUN pip install --no-cache-dir -r /tmp/requirements.txt

# =============================================================================
# 🚀 STAGE 2: RUNTIME - Imagem final de produção
# =============================================================================
FROM python:3.11-slim as runtime

# Configurações de runtime
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PATH="/opt/venv/bin:$PATH" \
    ENVIRONMENT=production \
    PORT=8000

# Instalar apenas dependências de runtime necessárias
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Criar usuário não-root para segurança
RUN groupadd -r appuser && useradd -r -g appuser -d /app -s /bin/bash appuser

# Copiar ambiente virtual do builder
COPY --from=builder /opt/venv /opt/venv

# Criar diretórios necessários
RUN mkdir -p /app/logs /app/outputs /app/data \
    && chown -R appuser:appuser /app

# Definir diretório de trabalho
WORKDIR /app

# Copiar código da aplicação
COPY --chown=appuser:appuser . .

# Criar script de healthcheck
COPY --chown=appuser:appuser <<EOF /app/healthcheck.py
#!/usr/bin/env python3
import sys
import requests
import os

def health_check():
    try:
        port = os.getenv('PORT', '8000')
        response = requests.get(f'http://localhost:{port}/health', timeout=10)
        if response.status_code == 200:
            print("Health check passed")
            sys.exit(0)
        else:
            print(f"Health check failed with status {response.status_code}")
            sys.exit(1)
    except Exception as e:
        print(f"Health check failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    health_check()
EOF

# Tornar healthcheck executável
RUN chmod +x /app/healthcheck.py

# Mudar para usuário não-root
USER appuser

# Expor porta da aplicação
EXPOSE $PORT

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD python /app/healthcheck.py

# Configurar volumes para dados persistentes
VOLUME ["/app/logs", "/app/outputs", "/app/data"]

# Comando padrão para executar a aplicação
CMD ["sh", "-c", "uvicorn app:app --host 0.0.0.0 --port ${PORT} --workers 1 --access-log --log-level info"]

# =============================================================================
# 🔧 ALTERNATIVE COMMANDS (comentados)
# =============================================================================
# Para usar Gunicorn (mais robusto para produção):
# CMD ["sh", "-c", "gunicorn app:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:${PORT} --access-logfile - --error-logfile -"]

# Para usar com múltiplos workers:
# CMD ["sh", "-c", "uvicorn app:app --host 0.0.0.0 --port ${PORT} --workers 4"]

# Para debug (não usar em produção):
# CMD ["sh", "-c", "uvicorn app:app --host 0.0.0.0 --port ${PORT} --reload --log-level debug"]
