# Makefile for audit-summary-ms Docker operations
# Author: Generated for AWS Lambda deployment
# Date: $(shell date +%Y-%m-%d)

# Variables
ECR_REGISTRY = 637423516204.dkr.ecr.us-east-1.amazonaws.com
ECR_REPOSITORY = audit-summary-ms
AWS_REGION = us-east-1
IMAGE_TAG = latest
PLATFORM = linux/amd64

# Full image name
IMAGE_NAME = $(ECR_REGISTRY)/$(ECR_REPOSITORY):$(IMAGE_TAG)

# Colors for output
RED = \033[0;31m
GREEN = \033[0;32m
YELLOW = \033[1;33m
BLUE = \033[0;34m
NC = \033[0m # No Color

# Default target
.PHONY: help
help: ## Show this help message
	@echo "$(BLUE)audit-summary-ms Docker Operations$(NC)"
	@echo "$(BLUE)====================================$(NC)"
	@echo ""
	@echo "Available targets:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  $(GREEN)%-20s$(NC) %s\n", $$1, $$2}'
	@echo ""
	@echo "$(YELLOW)Configuration:$(NC)"
	@echo "  ECR Registry: $(ECR_REGISTRY)"
	@echo "  Repository:   $(ECR_REPOSITORY)"
	@echo "  Image Tag:    $(IMAGE_TAG)"
	@echo "  Platform:     $(PLATFORM)"
	@echo "  AWS Region:   $(AWS_REGION)"

.PHONY: check-docker
check-docker: ## Check if Docker is running
	@echo "$(BLUE)Checking Docker...$(NC)"
	@docker --version > /dev/null 2>&1 || (echo "$(RED)Error: Docker is not installed or not running$(NC)" && exit 1)
	@echo "$(GREEN)✓ Docker is available$(NC)"

.PHONY: check-aws
check-aws: ## Check if AWS CLI is configured
	@echo "$(BLUE)Checking AWS CLI...$(NC)"
	@aws --version > /dev/null 2>&1 || (echo "$(RED)Error: AWS CLI is not installed$(NC)" && exit 1)
	@aws sts get-caller-identity > /dev/null 2>&1 || (echo "$(RED)Error: AWS CLI is not configured$(NC)" && exit 1)
	@echo "$(GREEN)✓ AWS CLI is configured$(NC)"

.PHONY: login
login: check-aws ## Login to ECR
	@echo "$(BLUE)Logging into ECR...$(NC)"
	@aws ecr get-login-password --region $(AWS_REGION) | docker login --username AWS --password-stdin $(ECR_REGISTRY)
	@echo "$(GREEN)✓ Successfully logged into ECR$(NC)"

.PHONY: build
build: check-docker ## Build Docker image for Lambda
	@echo "$(BLUE)Building Lambda Docker image...$(NC)"
	@echo "$(YELLOW)Image: $(IMAGE_NAME)$(NC)"
	@echo "$(YELLOW)Platform: $(PLATFORM)$(NC)"
	@docker build \
		--no-cache \
		--platform $(PLATFORM) \
		-t $(IMAGE_NAME) \
		-f Dockerfile \
		.
	@echo "$(GREEN)✓ Lambda image built successfully$(NC)"

.PHONY: build-cached
build-cached: check-docker ## Build Docker image with cache for Lambda
	@echo "$(BLUE)Building Lambda Docker image (with cache)...$(NC)"
	@echo "$(YELLOW)Image: $(IMAGE_NAME)$(NC)"
	@echo "$(YELLOW)Platform: $(PLATFORM)$(NC)"
	@docker build \
		--platform $(PLATFORM) \
		-t $(IMAGE_NAME) \
		-f Dockerfile \
		.
	@echo "$(GREEN)✓ Lambda image built successfully$(NC)"

.PHONY: build-prod
build-prod: check-docker ## Build production Docker image (standalone)
	@echo "$(BLUE)Building production Docker image...$(NC)"
	@echo "$(YELLOW)Image: audit-summary-ms:prod$(NC)"
	@echo "$(YELLOW)Platform: $(PLATFORM)$(NC)"
	@docker build \
		--no-cache \
		--platform $(PLATFORM) \
		-t audit-summary-ms:prod \
		-f Dockerfile.prod \
		.
	@echo "$(GREEN)✓ Production image built successfully$(NC)"

.PHONY: push
push: check-aws ## Push image to ECR
	@echo "$(BLUE)Pushing image to ECR...$(NC)"
	@echo "$(YELLOW)Image: $(IMAGE_NAME)$(NC)"
	@docker push $(IMAGE_NAME)
	@echo "$(GREEN)✓ Image pushed successfully$(NC)"

.PHONY: build-and-push
build-and-push: login build push ## Build and push Lambda image (full process)
	@echo "$(GREEN)✓ Build and push completed successfully$(NC)"

.PHONY: build-cached-and-push
build-cached-and-push: login build-cached push ## Build (with cache) and push Lambda image
	@echo "$(GREEN)✓ Build (cached) and push completed successfully$(NC)"

.PHONY: clean
clean: ## Remove local Docker images
	@echo "$(BLUE)Cleaning local images...$(NC)"
	@docker rmi $(IMAGE_NAME) 2>/dev/null || echo "$(YELLOW)Lambda image not found locally$(NC)"
	@docker rmi audit-summary-ms:prod 2>/dev/null || echo "$(YELLOW)Production image not found locally$(NC)"
	@docker system prune -f
	@echo "$(GREEN)✓ Cleanup completed$(NC)"

.PHONY: inspect
inspect: ## Inspect the built Lambda image
	@echo "$(BLUE)Inspecting Lambda image...$(NC)"
	@docker inspect $(IMAGE_NAME) 2>/dev/null || echo "$(RED)Image not found. Run 'make build' first.$(NC)"

.PHONY: run-local
run-local: ## Run Lambda container locally for testing
	@echo "$(BLUE)Running Lambda container locally...$(NC)"
	@echo "$(YELLOW)Note: This will run the Lambda handler locally on port 8080$(NC)"
	@docker run --rm -p 8080:8080 $(IMAGE_NAME)

.PHONY: run-prod
run-prod: ## Run production container locally
	@echo "$(BLUE)Running production container locally...$(NC)"
	@echo "$(YELLOW)Note: This will run FastAPI on port 8000$(NC)"
	@docker run --rm -p 8000:8000 audit-summary-ms:prod

.PHONY: shell
shell: ## Open shell in Lambda container
	@echo "$(BLUE)Opening shell in Lambda container...$(NC)"
	@docker run --rm -it --entrypoint /bin/bash $(IMAGE_NAME)

.PHONY: shell-prod
shell-prod: ## Open shell in production container
	@echo "$(BLUE)Opening shell in production container...$(NC)"
	@docker run --rm -it --entrypoint /bin/bash audit-summary-ms:prod

.PHONY: logs
logs: ## Show recent ECR repository information
	@echo "$(BLUE)ECR Repository Information:$(NC)"
	@aws ecr describe-repositories --repository-names $(ECR_REPOSITORY) --region $(AWS_REGION) 2>/dev/null || echo "$(RED)Repository not found$(NC)"
	@echo ""
	@echo "$(BLUE)Recent Images:$(NC)"
	@aws ecr describe-images --repository-name $(ECR_REPOSITORY) --region $(AWS_REGION) --max-items 10 --query 'imageDetails[*].[imageTags[0],imageDigest,imagePushedAt]' --output table 2>/dev/null || echo "$(RED)No images found$(NC)"

.PHONY: tag
tag: ## Tag image with custom tag (usage: make tag TAG=v1.0.0)
	@if [ -z "$(TAG)" ]; then \
		echo "$(RED)Error: TAG is required. Usage: make tag TAG=v1.0.0$(NC)"; \
		exit 1; \
	fi
	@echo "$(BLUE)Tagging image with $(TAG)...$(NC)"
	@docker tag $(IMAGE_NAME) $(ECR_REGISTRY)/$(ECR_REPOSITORY):$(TAG)
	@echo "$(GREEN)✓ Image tagged as $(ECR_REGISTRY)/$(ECR_REPOSITORY):$(TAG)$(NC)"

.PHONY: push-tag
push-tag: ## Push specific tag (usage: make push-tag TAG=v1.0.0)
	@if [ -z "$(TAG)" ]; then \
		echo "$(RED)Error: TAG is required. Usage: make push-tag TAG=v1.0.0$(NC)"; \
		exit 1; \
	fi
	@echo "$(BLUE)Pushing tagged image...$(NC)"
	@docker push $(ECR_REGISTRY)/$(ECR_REPOSITORY):$(TAG)
	@echo "$(GREEN)✓ Tagged image pushed successfully$(NC)"

.PHONY: release
release: ## Build, tag and push with version (usage: make release TAG=v1.0.0)
	@if [ -z "$(TAG)" ]; then \
		echo "$(RED)Error: TAG is required. Usage: make release TAG=v1.0.0$(NC)"; \
		exit 1; \
	fi
	@$(MAKE) login
	@$(MAKE) build
	@$(MAKE) tag TAG=$(TAG)
	@$(MAKE) push
	@$(MAKE) push-tag TAG=$(TAG)
	@echo "$(GREEN)✓ Release $(TAG) completed successfully$(NC)"

.PHONY: status
status: ## Show current status
	@echo "$(BLUE)Current Status:$(NC)"
	@echo "$(YELLOW)Docker Images:$(NC)"
	@docker images | grep $(ECR_REPOSITORY) || echo "  No local images found"
	@echo ""
	@echo "$(YELLOW)AWS Configuration:$(NC)"
	@aws sts get-caller-identity 2>/dev/null || echo "  AWS not configured"
	@echo ""
	@echo "$(YELLOW)ECR Login Status:$(NC)"
	@docker system info | grep -A 1 "Registry:" || echo "  Not logged in"

# Development targets
.PHONY: dev-setup
dev-setup: ## Setup development environment
	@echo "$(BLUE)Setting up development environment...$(NC)"
	@if [ -f "requirements.txt" ]; then \
		pip install -r requirements.txt; \
	elif [ -f "requirements-prod.txt" ]; then \
		pip install -r requirements-prod.txt; \
	else \
		echo "$(RED)No requirements file found$(NC)"; \
		exit 1; \
	fi
	@echo "$(GREEN)✓ Development environment ready$(NC)"

.PHONY: test
test: ## Run tests (if available)
	@echo "$(BLUE)Running tests...$(NC)"
	@if [ -d "tests" ]; then \
		python -m pytest tests/; \
	elif [ -f "test_*.py" ]; then \
		python -m pytest test_*.py; \
	else \
		echo "$(YELLOW)No tests found$(NC)"; \
	fi

.PHONY: lint
lint: ## Run code linting
	@echo "$(BLUE)Running linting...$(NC)"
	@if command -v flake8 > /dev/null; then \
		flake8 . --exclude=venv,env,__pycache__ --max-line-length=120; \
	else \
		echo "$(YELLOW)flake8 not installed. Run: pip install flake8$(NC)"; \
	fi

# Quick commands
.PHONY: quick-deploy
quick-deploy: build-cached-and-push ## Quick deploy (build with cache and push)

.PHONY: full-deploy
full-deploy: build-and-push ## Full deploy (clean build and push)

.PHONY: timestamp-deploy
timestamp-deploy: ## Deploy with timestamp tag
	@$(eval TIMESTAMP := $(shell date +%Y%m%d-%H%M%S))
	@echo "$(BLUE)Deploying with timestamp: $(TIMESTAMP)$(NC)"
	@$(MAKE) login
	@$(MAKE) build
	@$(MAKE) tag TAG=$(TIMESTAMP)
	@$(MAKE) push
	@$(MAKE) push-tag TAG=$(TIMESTAMP)
	@echo "$(GREEN)✓ Timestamp deploy $(TIMESTAMP) completed successfully$(NC)"

# Utility targets
.PHONY: size
size: ## Show image sizes
	@echo "$(BLUE)Image Sizes:$(NC)"
	@docker images | grep $(ECR_REPOSITORY) | awk '{print $$1":"$$2" - "$$7" "$$8}'

.PHONY: history
history: ## Show image build history
	@echo "$(BLUE)Image Build History:$(NC)"
	@docker history $(IMAGE_NAME) 2>/dev/null || echo "$(RED)Image not found. Run 'make build' first.$(NC)"
