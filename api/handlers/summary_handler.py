from fastapi import APIRouter, Depends
from typing import Dict, Any
from ...core.models.summary_models import SummaryRequest, GeneralSummaryRequest
from ...services.summary_service import SummaryService
from ...repositories.user_repository import UserRepository
from ...core.config.database import DatabaseConfig, DatabaseConnectionFactory

router = APIRouter()

def get_user_repository(db_name: str) -> UserRepository:
    db_config = DatabaseConfig(
        host="audta-ai-instance.cdkyomc4a6x4.us-east-1.rds.amazonaws.com",
        database=db_name,
        user="devdb",
        password="jtNr6=LdR+R6aF4-d~J"
    )
    factory = DatabaseConnectionFactory(db_config)
    connection = factory.create_connection()
    if connection:
        return UserRepository(connection)
    else:
        raise Exception("Não foi possível conectar ao banco de dados")

def get_summary_service() -> SummaryService:
    user_repo_landzone = get_user_repository("audit_landzone")
    user_repo_gold = get_user_repository("audit_gold")
    return SummaryService(user_repo_landzone, user_repo_gold)

@router.post("/summary/criterion")
async def get_summary_by_criterion(
    request: SummaryRequest,
    service: SummaryService = Depends(get_summary_service)
) -> Dict[str, Any]:
    return await service.get_summary_by_criterion(request)

@router.post("/summary/general")
async def get_general_summary(
    request: GeneralSummaryRequest,
    service: SummaryService = Depends(get_summary_service)
) -> Dict[str, Any]:
    return await service.get_general_summary(request) 