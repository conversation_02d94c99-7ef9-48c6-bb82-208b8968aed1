from fastapi import FastAPI
import uvicorn
from src.api.handlers.summary_handler import router as summary_router
from pydantic import BaseModel
from typing import Optional
from mangum import Mangum
from fastapi.middleware.cors import CORSMiddleware
import sys
import json
import logging 

class JsonFormatter(logging.Formatter):
    def format(self, record):
        json_record = {
            "timestamp": self.formatTime(record),
            "level": record.levelname,
            "message": record.getMessage(),
            "module": record.module,
            "request_id": getattr(record, "request_id", "no_request_id"),
        }
        if record.exc_info:
            json_record["exc_info"] = self.formatException(record.exc_info)
        return json.dumps(json_record)

logger = logging.getLogger()
logger.setLevel(logging.INFO)

handler = logging.StreamHandler(sys.stdout)
handler.setFormatter(JsonFormatter())
logger.addHandler(handler)

app = FastAPI()
origins = ["*"]
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=[""],
    allow_headers=[""],
)

app.include_router(summary_router)
handler = Mangum(app, lifespan="off")