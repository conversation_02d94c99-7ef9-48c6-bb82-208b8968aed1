# Conteúdo do arquivo /fastapi-app/fastapi-app/src/controllers/__init__.py

class ResumoController:
    def resumo_por_criterio(self, fk_id_platform: int, fk_id_criterion: int, start_date: str, end_date: str):
        # Lógica para obter resumo por critério
        pass

    def resumo_geral(self, fk_id_platform: int, start_date: str, end_date: str):
        # Lógica para obter resumo geral
        pass