from dataclasses import dataclass
import pymysql
from pymysql import <PERSON><PERSON><PERSON>

@dataclass
class DatabaseConfig:
    host: str
    database: str
    user: str
    password: str

class DatabaseConnectionFactory:
    def __init__(self, config: DatabaseConfig):
        self.config = config

    def create_connection(self):
        try:
            connection = pymysql.connect(
                host=self.config.host,
                database=self.config.database,
                user=self.config.user,
                password=self.config.password,
                cursorclass=pymysql.cursors.DictCursor
            )
            return connection
        except Error as e:
            print(f"Erro ao conectar ao banco de dados: {e}")
            return None 