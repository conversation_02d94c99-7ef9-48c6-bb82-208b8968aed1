from pydantic import BaseModel
from typing import Optional

class GeneralSummaryRequest(BaseModel):
    fk_id_platform: int
    start_date: str
    end_date: str
    attribute_name: Optional[str] = None
    attribute_value: Optional[list[str]] = None

class SummaryRequest(BaseModel):
    fk_id_platform: int
    criterion_label: Optional[str] = None
    start_date: str
    end_date: str
    attribute_name: Optional[str] = None
    attribute_value: Optional[list[str]] = None 