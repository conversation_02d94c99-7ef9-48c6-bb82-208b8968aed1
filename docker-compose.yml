# =============================================================================
# 🐳 DOCKER COMPOSE - AUDIT SUMMARY MICROSERVICE
# =============================================================================
# Configuração completa para produção com todos os serviços
# Inclui API, cache, monitoramento e proxy reverso
# =============================================================================

version: '3.8'

# =============================================================================
# 🔧 SERVICES
# =============================================================================
services:
  
  # 🚀 Aplicação Principal - FastAPI
  audit-summary-api:
    build:
      context: .
      dockerfile: Dockerfile.prod
      args:
        BUILD_DATE: ${BUILD_DATE:-$(date -u +'%Y-%m-%dT%H:%M:%SZ')}
        VCS_REF: ${VCS_REF:-$(git rev-parse --short HEAD)}
        VERSION: ${VERSION:-1.0}
    image: audit-summary-ms:${VERSION:-latest}
    container_name: audit-summary-api
    restart: unless-stopped
    
    # Portas
    ports:
      - "${API_PORT:-8000}:8000"
    
    # Environment Variables
    environment:
      - ENVIRONMENT=${ENVIRONMENT:-production}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - DB_HOST=${DB_HOST}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_LANDZONE=${DB_LANDZONE:-audit_landzone}
      - DB_GOLD=${DB_GOLD:-audit_gold}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - MODEL_NAME=${MODEL_NAME:-claude-3-5-sonnet-20241022}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION_NAME=${AWS_REGION_NAME:-us-east-1}
      - REDIS_URL=redis://redis:6379/0
    
    # Volumes
    volumes:
      - ./logs:/app/logs
      - ./outputs:/app/outputs
      - ./data:/app/data
    
    # Dependências
    depends_on:
      redis:
        condition: service_healthy
    
    # Health Check
    healthcheck:
      test: ["CMD", "python", "/app/healthcheck.py"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Recursos
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    
    # Logging
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    
    # Networks
    networks:
      - audit-network

  # 🗄️ Cache Redis
  redis:
    image: redis:7-alpine
    container_name: audit-redis
    restart: unless-stopped
    
    # Portas (apenas interno)
    expose:
      - "6379"
    
    # Configuração Redis
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    
    # Volumes
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
    
    # Health Check
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    
    # Recursos
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 128M
    
    # Networks
    networks:
      - audit-network

  # 🌐 Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: audit-nginx
    restart: unless-stopped
    
    # Portas
    ports:
      - "${NGINX_HTTP_PORT:-80}:80"
      - "${NGINX_HTTPS_PORT:-443}:443"
    
    # Volumes
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    
    # Dependências
    depends_on:
      audit-summary-api:
        condition: service_healthy
    
    # Health Check
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    
    # Networks
    networks:
      - audit-network

  # 📊 Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: audit-prometheus
    restart: unless-stopped
    
    # Portas
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    
    # Volumes
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    
    # Comando
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    
    # Networks
    networks:
      - audit-network

  # 📈 Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: audit-grafana
    restart: unless-stopped
    
    # Portas
    ports:
      - "${GRAFANA_PORT:-3000}:3000"
    
    # Environment
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
    
    # Volumes
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards:ro
    
    # Dependências
    depends_on:
      - prometheus
    
    # Networks
    networks:
      - audit-network

  # 📓 Jupyter Notebook (Opcional - apenas para desenvolvimento)
  jupyter:
    build:
      context: .
      dockerfile: Dockerfile.jupyter
    container_name: audit-jupyter
    restart: unless-stopped
    profiles: ["development", "analysis"]
    
    # Portas
    ports:
      - "${JUPYTER_PORT:-8888}:8888"
    
    # Environment
    environment:
      - JUPYTER_TOKEN=${JUPYTER_TOKEN:-audit-summary-token}
      - JUPYTER_ENABLE_LAB=yes
    
    # Volumes
    volumes:
      - ./notebooks:/app/notebooks
      - ./data:/app/data
      - ./outputs:/app/outputs
      - jupyter_data:/home/<USER>/.jupyter
    
    # Networks
    networks:
      - audit-network

# =============================================================================
# 📦 VOLUMES
# =============================================================================
volumes:
  # Dados persistentes Redis
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/redis

  # Dados Prometheus
  prometheus_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/prometheus

  # Dados Grafana
  grafana_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/grafana

  # Dados Jupyter
  jupyter_data:
    driver: local

# =============================================================================
# 🌐 NETWORKS
# =============================================================================
networks:
  audit-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# =============================================================================
# 🔧 CONFIGS & SECRETS (Docker Swarm)
# =============================================================================
configs:
  nginx_config:
    file: ./nginx/nginx.conf
  prometheus_config:
    file: ./monitoring/prometheus.yml

secrets:
  db_password:
    external: true
  openai_api_key:
    external: true
  anthropic_api_key:
    external: true
