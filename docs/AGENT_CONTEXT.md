# 🤖 Agent Context - Audit Summary Microservice

## 📋 Contexto para Agentes IA

Este documento fornece contexto completo para agentes IA que trabalharão com o **Audit Summary Microservice**, um sistema de análise e geração de resumos de auditorias usando técnicas de IA avançadas.

## 🎯 Origem e Propósito

### Objetivo Principal
Sistema de auditoria automatizada que utiliza **LLM as Judge** e **Self-Reflection** para:
- Classificar e analisar atendimentos de customer service
- Gerar resumos inteligentes baseados em critérios específicos
- Fornecer insights sobre pontos fortes, fracos, oportunidades e recomendações

### Contexto de Negócio
- **Domínio**: Customer Service Analytics & Quality Assurance
- **Usuários**: Equipes de qualidade, gestores, analistas de atendimento
- **Dados**: Conversas de chat, critérios de avaliação, métricas de performance

## 🏗️ Arquitetura do Sistema

### Stack Tecnológica
```yaml
Backend:
  - Framework: FastAPI (Python 3.9+)
  - Database: MySQL (AWS RDS)
  - Deployment: AWS Lambda (via Mangum)
  
AI/ML Services:
  - OpenAI GPT Models
  - Anthropic Claude (3.5 Sonnet/Haiku)
  - AWS Bedrock (Llama models)
  - LiteLLM (Multi-provider abstraction)

Infrastructure:
  - AWS RDS (MySQL databases)
  - AWS Secrets Manager
  - Docker containerization
  - Jupyter Notebooks para análise
```

### Arquitetura de Camadas
```
┌─────────────────────────────────────┐
│           API Layer                 │
│  ┌─────────────────────────────────┐│
│  │ FastAPI + Mangum (Lambda)       ││
│  │ - summary_handler.py            ││
│  │ - CORS middleware               ││
│  │ - JSON logging                  ││
│  └─────────────────────────────────┘│
└─────────────────────────────────────┘
┌─────────────────────────────────────┐
│         Service Layer               │
│  ┌─────────────────────────────────┐│
│  │ SummaryService                  ││
│  │ - Business logic                ││
│  │ - Data orchestration            ││
│  └─────────────────────────────────┘│
└─────────────────────────────────────┘
┌─────────────────────────────────────┐
│       Repository Layer              │
│  ┌─────────────────────────────────┐│
│  │ UserRepository                  ││
│  │ - Database queries              ││
│  │ - AI model integration          ││
│  │ - Data processing               ││
│  └─────────────────────────────────┘│
└─────────────────────────────────────┘
┌─────────────────────────────────────┐
│         Data Layer                  │
│  ┌─────────────────────────────────┐│
│  │ MySQL Databases                 ││
│  │ - audit_landzone (raw data)     ││
│  │ - audit_gold (processed data)   ││
│  └─────────────────────────────────┘│
└─────────────────────────────────────┘
```

## 🔧 Configuração e Environment

### Variáveis de Ambiente Críticas
```bash
# Database Connections
DB_HOST=audta-ai-instance.cdkyomc4a6x4.us-east-1.rds.amazonaws.com
DB_LANDZONE=audit_landzone    # Raw conversation data
DB_GOLD=audit_gold           # Processed audit results

# AI Model Configuration
MODEL_NAME=claude-3-5-sonnet-20241022  # Primary model
OPENAI_API_KEY=sk-...
ANTHROPIC_API_KEY=sk-ant-...

# AWS Services
AWS_REGION_NAME=us-east-1
AWS_SECRET_NAME=prod/audie   # Secrets Manager
```

### Databases Schema Context
```sql
-- audit_landzone: Raw conversation data
Tables:
- audit_chats: Chat conversations
- audit_chat_attributes: Chat metadata/attributes
- audit_action: User actions/events

-- audit_gold: Processed audit results  
Tables:
- criterion_results: AI evaluation results
- platform_data: Platform configurations
```

## ⚠️ Pontos de Atenção Críticos

### 1. **Segurança - Credenciais Expostas**
```python
# ❌ PROBLEMA CRÍTICO: Hardcoded credentials
password="jtNr6=LdR+R6aF4-d~J"
aws_secret_access_key="Ks3MM/R+FLxEe+kCLkRw4CEszeCO6T+Km1ZNOH+P"
```
**Ação Necessária**: Migrar para AWS Secrets Manager ou variáveis de ambiente

### 2. **Arquitetura - Código Duplicado**
- `routes/routes.py` (legacy) vs `api/handlers/summary_handler.py` (novo)
- Modelos Pydantic duplicados
- Lógica de conexão DB repetida

### 3. **Performance - Conexões DB**
```python
# Cada request cria nova conexão
def get_user_repository(db_name: str):
    # Nova conexão a cada chamada
```
**Recomendação**: Implementar connection pooling

### 4. **Imports Inconsistentes**
```python
# Mistura de padrões
from src.api.handlers.summary_handler import router
from ...core.models.summary_models import SummaryRequest
```

## 🧪 Testing Strategy

### Tipos de Teste Necessários
```python
# Unit Tests
- test_summary_service.py
- test_user_repository.py
- test_database_config.py

# Integration Tests  
- test_api_endpoints.py
- test_database_connections.py
- test_ai_model_integration.py

# E2E Tests
- test_summary_generation_flow.py
```

### Mock Strategy para IA
```python
# Mock AI responses para testes
mock_responses = {
    "claude": {"pontos_fortes": [...], "pontos_fracos": [...]},
    "gpt": {"summary": "...", "recommendations": [...]}
}
```

## 📊 Monitoramento e Observabilidade

### Logging Structure
```python
# JSON structured logging já implementado
{
    "timestamp": "2025-01-20T10:00:00Z",
    "level": "INFO", 
    "message": "Query executed",
    "module": "user_repository",
    "request_id": "req-123",
    "duration": 0.1234
}
```

### Métricas Importantes
- **Database Query Performance**: Decorator `@measure_time` já implementado
- **AI Model Response Times**: Por modelo (Claude, GPT, Bedrock)
- **API Response Times**: Por endpoint
- **Error Rates**: Por tipo de erro

## 🔄 Workflow de Desenvolvimento

### 1. **Análise Exploratória**
```python
# Jupyter Notebook: Auditoria_criacao_resumos.ipynb
- Análise de dados de conversas
- Teste de prompts de IA
- Validação de critérios de auditoria
```

### 2. **Desenvolvimento de Features**
```python
# Fluxo típico:
1. Definir modelo Pydantic (core/models/)
2. Implementar lógica de negócio (services/)
3. Criar endpoint FastAPI (api/handlers/)
4. Adicionar testes
5. Documentar
```

### 3. **Deploy Pipeline**
```bash
# Local Development
uvicorn app:app --reload

# Docker Build
docker build -t audit-summary-ms .

# AWS Lambda Deploy
# Via Mangum handler já configurado
```

## 🎯 Objetivos de Melhoria

### Refatoração Prioritária
1. **Consolidar Rotas**: Migrar `routes/` para `api/handlers/`
2. **Environment Variables**: Remover hardcoded credentials
3. **Connection Pooling**: Otimizar conexões DB
4. **Error Handling**: Padronizar tratamento de erros
5. **Testing**: Implementar suite de testes completa

### Novas Features Sugeridas
1. **Caching**: Redis para responses de IA
2. **Rate Limiting**: Controle de uso de APIs
3. **Async Processing**: Para summaries longos
4. **Webhook Support**: Notificações de conclusão
5. **Multi-tenant**: Suporte a múltiplas plataformas

## 📚 Referências Técnicas

### Papers de Referência (do notebook)
- **LLM as Judge**: https://proceedings.neurips.cc/paper_files/paper/2023/hash/91f18a1287b398d378ef22505bf41832-Abstract-Datasets_and_Benchmarks.html
- **Self-Reflection**: https://arxiv.org/abs/2410.20774
- **Advanced Techniques**: https://arxiv.org/abs/2408.13006

### APIs Utilizadas
```python
# AI Models
- OpenAI GPT-4/3.5
- Anthropic Claude 3.5 Sonnet/Haiku  
- AWS Bedrock Llama models
- LiteLLM abstraction layer

# AWS Services
- RDS MySQL
- Secrets Manager
- Lambda (via Mangum)
```

## 🔍 Debugging e Troubleshooting

### Logs Importantes
```bash
# Database connection issues
grep "Erro ao conectar" logs/

# AI model failures  
grep "chat_completion_request" logs/

# Performance issues
grep "executada em" logs/
```

### Common Issues
1. **DB Connection Timeout**: Verificar RDS security groups
2. **AI API Rate Limits**: Implementar retry logic
3. **Lambda Cold Start**: Otimizar imports
4. **Memory Issues**: Monitorar uso em notebooks

---

**Para Agentes IA**: Este sistema processa dados sensíveis de customer service. Sempre priorize segurança, performance e qualidade dos insights gerados. Use este contexto para entender o domínio antes de fazer modificações.

**Versão**: 1.0 | **Data**: 2025-01-20
