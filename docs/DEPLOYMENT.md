# 🚀 Deployment Guide - Audit Summary Microservice

## 📋 Visão Geral

Este guia fornece instruções completas para deploy do **Audit Summary Microservice** em diferentes ambientes, desde desenvolvimento local até produção em AWS.

## 🏗️ Ambientes de Deploy

### 1. 🖥️ Desenvolvimento Local

#### Pré-requisitos
- Python 3.9+
- MySQL local ou acesso ao RDS
- API keys configuradas

#### Setup Rápido
```bash
# Clone e setup
git clone <repository>
cd audit-summary-ms

# Ambiente virtual
python -m venv venv
source venv/bin/activate

# Dependências
pip install -r requirements.txt

# Configuração
cp .env.example .env
# Editar .env com suas credenciais

# Executar
uvicorn app:app --reload --host 0.0.0.0 --port 8000
```

### 2. 🐳 Docker Local

#### Docker Compose Development
```bash
# Build e run
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --build

# Apenas API
docker-compose up audit-summary-dev

# Com database local
docker-compose up audit-summary-dev mysql-dev
```

#### Verificação
```bash
# Health check
curl http://localhost:8000/health

# API docs
open http://localhost:8000/docs

# Jupyter (se habilitado)
open http://localhost:8888
```

### 3. ☁️ AWS Lambda (Serverless)

#### Configuração Atual
O projeto já está configurado para Lambda via **Mangum**:

```python
# app.py
from mangum import Mangum
handler = Mangum(app, lifespan="off")
```

#### Deploy com AWS SAM
```yaml
# template.yaml
AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31

Resources:
  AuditSummaryFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: .
      Handler: app.handler
      Runtime: python3.11
      Timeout: 300
      MemorySize: 1024
      Environment:
        Variables:
          ENVIRONMENT: production
          MODEL_NAME: claude-3-5-sonnet-20241022
      Events:
        Api:
          Type: Api
          Properties:
            Path: /{proxy+}
            Method: ANY
```

#### Comandos de Deploy
```bash
# Build
sam build

# Deploy
sam deploy --guided

# Logs
sam logs -n AuditSummaryFunction --tail
```

#### ⚠️ Limitações Lambda
- **Timeout**: Máximo 15 minutos
- **Memory**: Máximo 10GB
- **Cold Start**: Pode afetar performance
- **Concurrent Executions**: Limite de 1000 por região

### 4. 🖥️ AWS ECS (Containerized)

#### Task Definition
```json
{
  "family": "audit-summary-ms",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "512",
  "memory": "1024",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "taskRoleArn": "arn:aws:iam::account:role/ecsTaskRole",
  "containerDefinitions": [
    {
      "name": "audit-summary-api",
      "image": "your-account.dkr.ecr.region.amazonaws.com/audit-summary-ms:latest",
      "portMappings": [
        {
          "containerPort": 8000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "ENVIRONMENT",
          "value": "production"
        }
      ],
      "secrets": [
        {
          "name": "DB_PASSWORD",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:audit-db-password"
        },
        {
          "name": "OPENAI_API_KEY", 
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:openai-key"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/audit-summary-ms",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

#### Deploy ECS
```bash
# Build e push para ECR
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin account.dkr.ecr.us-east-1.amazonaws.com

docker build -t audit-summary-ms .
docker tag audit-summary-ms:latest account.dkr.ecr.us-east-1.amazonaws.com/audit-summary-ms:latest
docker push account.dkr.ecr.us-east-1.amazonaws.com/audit-summary-ms:latest

# Registrar task definition
aws ecs register-task-definition --cli-input-json file://task-definition.json

# Criar ou atualizar serviço
aws ecs update-service --cluster audit-cluster --service audit-summary-service --task-definition audit-summary-ms:1
```

### 5. ⚡ AWS App Runner

#### apprunner.yaml
```yaml
version: 1.0
runtime: python3
build:
  commands:
    build:
      - pip install -r requirements.txt
run:
  runtime-version: 3.11
  command: uvicorn app:app --host 0.0.0.0 --port 8000
  network:
    port: 8000
    env: PORT
  env:
    - name: ENVIRONMENT
      value: production
```

#### Deploy App Runner
```bash
# Via CLI
aws apprunner create-service \
  --service-name audit-summary-ms \
  --source-configuration '{
    "ImageRepository": {
      "ImageIdentifier": "account.dkr.ecr.region.amazonaws.com/audit-summary-ms:latest",
      "ImageConfiguration": {
        "Port": "8000"
      },
      "ImageRepositoryType": "ECR"
    },
    "AutoDeploymentsEnabled": true
  }'
```

## 🔧 Configuração de Produção

### 1. Environment Variables
```bash
# Produção - usar AWS Secrets Manager
export DB_HOST=prod-rds-endpoint.amazonaws.com
export DB_PASSWORD=$(aws secretsmanager get-secret-value --secret-id prod/db-password --query SecretString --output text)
export OPENAI_API_KEY=$(aws secretsmanager get-secret-value --secret-id prod/openai-key --query SecretString --output text)
export ENVIRONMENT=production
export LOG_LEVEL=INFO
```

### 2. Database Configuration
```python
# Produção - connection pooling
from sqlalchemy import create_engine
from sqlalchemy.pool import QueuePool

engine = create_engine(
    f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}/{DB_NAME}",
    poolclass=QueuePool,
    pool_size=10,
    max_overflow=20,
    pool_pre_ping=True,
    pool_recycle=3600
)
```

### 3. Logging Configuration
```python
# Produção - structured logging
import structlog

structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)
```

## 📊 Monitoramento e Observabilidade

### 1. Health Checks
```python
# app.py - adicionar endpoint
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0",
        "environment": os.getenv("ENVIRONMENT", "unknown")
    }

@app.get("/ready")
async def readiness_check():
    # Verificar dependências críticas
    try:
        # Test database connection
        db_status = await test_database_connection()
        # Test AI service
        ai_status = await test_ai_service()
        
        if db_status and ai_status:
            return {"status": "ready"}
        else:
            raise HTTPException(status_code=503, detail="Service not ready")
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Service not ready: {str(e)}")
```

### 2. Métricas Prometheus
```python
from prometheus_client import Counter, Histogram, generate_latest

# Métricas customizadas
REQUEST_COUNT = Counter('http_requests_total', 'Total HTTP requests', ['method', 'endpoint'])
REQUEST_DURATION = Histogram('http_request_duration_seconds', 'HTTP request duration')
AI_MODEL_REQUESTS = Counter('ai_model_requests_total', 'AI model requests', ['model', 'status'])

@app.get("/metrics")
async def metrics():
    return Response(generate_latest(), media_type="text/plain")
```

### 3. CloudWatch Logs
```python
import boto3
import json

cloudwatch_logs = boto3.client('logs')

def send_to_cloudwatch(log_group, log_stream, message):
    cloudwatch_logs.put_log_events(
        logGroupName=log_group,
        logStreamName=log_stream,
        logEvents=[
            {
                'timestamp': int(time.time() * 1000),
                'message': json.dumps(message)
            }
        ]
    )
```

## 🔒 Segurança em Produção

### 1. Secrets Management
```bash
# Criar secrets no AWS Secrets Manager
aws secretsmanager create-secret \
  --name "prod/audit-summary/db-password" \
  --description "Database password for audit summary service" \
  --secret-string "your-secure-password"

aws secretsmanager create-secret \
  --name "prod/audit-summary/openai-key" \
  --description "OpenAI API key" \
  --secret-string "sk-your-openai-key"
```

### 2. IAM Roles e Policies
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "secretsmanager:GetSecretValue"
      ],
      "Resource": [
        "arn:aws:secretsmanager:us-east-1:account:secret:prod/audit-summary/*"
      ]
    },
    {
      "Effect": "Allow",
      "Action": [
        "rds:DescribeDBInstances",
        "rds:Connect"
      ],
      "Resource": "*"
    }
  ]
}
```

### 3. Network Security
```yaml
# Security Groups
SecurityGroup:
  Type: AWS::EC2::SecurityGroup
  Properties:
    GroupDescription: Security group for Audit Summary MS
    VpcId: !Ref VPC
    SecurityGroupIngress:
      - IpProtocol: tcp
        FromPort: 8000
        ToPort: 8000
        SourceSecurityGroupId: !Ref LoadBalancerSecurityGroup
    SecurityGroupEgress:
      - IpProtocol: tcp
        FromPort: 443
        ToPort: 443
        CidrIp: 0.0.0.0/0  # HTTPS outbound
      - IpProtocol: tcp
        FromPort: 3306
        ToPort: 3306
        DestinationSecurityGroupId: !Ref DatabaseSecurityGroup
```

## 🔄 CI/CD Pipeline

### GitHub Actions
```yaml
name: Deploy Audit Summary MS

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      - name: Install dependencies
        run: |
          pip install -r requirements.txt
          pip install pytest pytest-cov
      - name: Run tests
        run: pytest --cov=src tests/

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
      
      - name: Build and push Docker image
        run: |
          aws ecr get-login-password | docker login --username AWS --password-stdin $ECR_REGISTRY
          docker build -t $ECR_REGISTRY/audit-summary-ms:$GITHUB_SHA .
          docker push $ECR_REGISTRY/audit-summary-ms:$GITHUB_SHA
      
      - name: Deploy to ECS
        run: |
          aws ecs update-service --cluster audit-cluster --service audit-summary-service --force-new-deployment
```

## 🔍 Troubleshooting

### Problemas Comuns

#### 1. Lambda Cold Start
```python
# Otimização para Lambda
import os
if os.getenv('AWS_LAMBDA_FUNCTION_NAME'):
    # Inicialização otimizada para Lambda
    from mangum import Mangum
    handler = Mangum(app, lifespan="off")
```

#### 2. Database Connection Issues
```bash
# Verificar conectividade
telnet your-rds-endpoint.amazonaws.com 3306

# Verificar security groups
aws ec2 describe-security-groups --group-ids sg-xxxxxxxxx

# Testar conexão
mysql -h your-rds-endpoint.amazonaws.com -u username -p
```

#### 3. Memory Issues
```python
# Monitorar uso de memória
import psutil
import logging

def log_memory_usage():
    memory = psutil.virtual_memory()
    logging.info(f"Memory usage: {memory.percent}%")
```

### Logs e Debugging
```bash
# CloudWatch Logs
aws logs tail /aws/lambda/audit-summary-function --follow

# ECS Logs
aws logs tail /ecs/audit-summary-ms --follow

# App Runner Logs
aws apprunner describe-service --service-arn your-service-arn
```

## 📈 Scaling e Performance

### Auto Scaling (ECS)
```json
{
  "ServiceName": "audit-summary-service",
  "ScalableDimension": "ecs:service:DesiredCount",
  "MinCapacity": 2,
  "MaxCapacity": 10,
  "TargetTrackingScalingPolicies": [
    {
      "TargetValue": 70.0,
      "PredefinedMetricSpecification": {
        "PredefinedMetricType": "ECSServiceAverageCPUUtilization"
      }
    }
  ]
}
```

### Load Balancer Configuration
```yaml
LoadBalancer:
  Type: AWS::ElasticLoadBalancingV2::LoadBalancer
  Properties:
    Type: application
    Scheme: internet-facing
    SecurityGroups: [!Ref LoadBalancerSecurityGroup]
    Subnets: [!Ref PublicSubnet1, !Ref PublicSubnet2]

TargetGroup:
  Type: AWS::ElasticLoadBalancingV2::TargetGroup
  Properties:
    Port: 8000
    Protocol: HTTP
    TargetType: ip
    VpcId: !Ref VPC
    HealthCheckPath: /health
    HealthCheckIntervalSeconds: 30
    HealthyThresholdCount: 2
    UnhealthyThresholdCount: 5
```

---

**Versão**: 1.0 | **Data**: 2025-01-20
