# 🐳 Docker Guide - Audit Summary Microservice

## 📋 Visão Geral

Este guia fornece instruções completas para containerização e deploy do **Audit Summary Microservice** usando Docker.

## 🏗️ Dockerfile Explicado

### Estrutura Atual
```dockerfile
FROM python:3.11-slim

# Otimizações de performance
ENV PIP_NO_CACHE_DIR=1 PYTHONDONTWRITEBYTECODE=1 PYTHONUNBUFFERED=1

WORKDIR /app

# Instalação de dependências
COPY requirements.txt /app/requirements.txt
RUN if [ -f requirements.txt ]; then pip install -r requirements.txt; fi

# Ferramentas para notebooks
RUN pip install jupyter papermill nbconvert

# Copia notebooks
COPY ./*.ipynb /app/

# Configuração para notebooks
ARG NB_FILE=Auditoria_criacao_resumos.ipynb
ENV NB_FILE=$NB_FILE

RUN mkdir -p /outputs
CMD bash -lc 'papermill "$NB_FILE" /outputs/run.ipynb && jupyter nbconvert --to html /outputs/run.ipynb --output /outputs/run.html && echo "Executado: /outputs/run.ipynb e /outputs/run.html" && tail -f /dev/null'
```

### ⚠️ Problemas Identificados
1. **Foco em Notebooks**: Dockerfile atual é para notebooks, não para API
2. **Falta FastAPI**: Não instala dependências da API
3. **CMD Inadequado**: Não executa o servidor FastAPI

## 🔧 Dockerfile Melhorado

### Para Produção (FastAPI)
```dockerfile
# Multi-stage build para otimização
FROM python:3.11-slim as builder

# Instalar dependências de build
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Criar ambiente virtual
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copiar e instalar dependências
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Stage final
FROM python:3.11-slim

# Criar usuário não-root
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Copiar ambiente virtual
COPY --from=builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Configurar diretório de trabalho
WORKDIR /app

# Copiar código da aplicação
COPY --chown=appuser:appuser . .

# Mudar para usuário não-root
USER appuser

# Expor porta
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Comando para executar a aplicação
CMD ["uvicorn", "app:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Para Desenvolvimento
```dockerfile
FROM python:3.11-slim

# Instalar dependências do sistema
RUN apt-get update && apt-get install -y \
    curl \
    vim \
    git \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Instalar dependências Python
COPY requirements.txt .
RUN pip install -r requirements.txt

# Instalar ferramentas de desenvolvimento
RUN pip install black isort flake8 mypy pytest

# Copiar código
COPY . .

# Expor portas
EXPOSE 8000 8888

# Comando padrão para desenvolvimento
CMD ["uvicorn", "app:app", "--reload", "--host", "0.0.0.0", "--port", "8000"]
```

## 🐙 Docker Compose

### docker-compose.yml
```yaml
version: '3.8'

services:
  # Aplicação principal
  audit-summary-api:
    build: 
      context: .
      dockerfile: Dockerfile.prod
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - DB_HOST=${DB_HOST}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
    volumes:
      - ./logs:/app/logs
    depends_on:
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Cache Redis
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  # Jupyter para análise
  jupyter:
    build:
      context: .
      dockerfile: Dockerfile.jupyter
    ports:
      - "8888:8888"
    environment:
      - JUPYTER_TOKEN=${JUPYTER_TOKEN}
    volumes:
      - ./notebooks:/app/notebooks
      - ./data:/app/data
      - ./outputs:/app/outputs
    restart: unless-stopped

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - audit-summary-api
    restart: unless-stopped

volumes:
  redis_data:
```

### docker-compose.dev.yml
```yaml
version: '3.8'

services:
  audit-summary-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "8000:8000"
      - "8888:8888"
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
    volumes:
      - .:/app
      - /app/__pycache__
      - /app/.pytest_cache
    stdin_open: true
    tty: true

  # Database local para desenvolvimento
  mysql-dev:
    image: mysql:8.0
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=rootpassword
      - MYSQL_DATABASE=audit_dev
      - MYSQL_USER=devuser
      - MYSQL_PASSWORD=devpassword
    volumes:
      - mysql_dev_data:/var/lib/mysql
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql

volumes:
  mysql_dev_data:
```

## 🚀 Comandos Docker

### Build e Run Básico
```bash
# Build da imagem
docker build -t audit-summary-ms .

# Run simples
docker run -p 8000:8000 audit-summary-ms

# Run com environment variables
docker run -p 8000:8000 --env-file .env audit-summary-ms

# Run em background
docker run -d -p 8000:8000 --name audit-api audit-summary-ms
```

### Docker Compose
```bash
# Desenvolvimento
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

# Produção
docker-compose up -d

# Build e run
docker-compose up --build

# Logs
docker-compose logs -f audit-summary-api

# Parar serviços
docker-compose down

# Limpar volumes
docker-compose down -v
```

### Comandos Úteis
```bash
# Entrar no container
docker exec -it audit-api bash

# Ver logs
docker logs audit-api -f

# Inspecionar container
docker inspect audit-api

# Ver recursos utilizados
docker stats audit-api

# Limpar imagens não utilizadas
docker image prune -a

# Backup de volume
docker run --rm -v audit-summary-ms_redis_data:/data -v $(pwd):/backup alpine tar czf /backup/redis_backup.tar.gz -C /data .
```

## 🔧 Configurações Avançadas

### Multi-stage Build Otimizado
```dockerfile
# Stage 1: Dependencies
FROM python:3.11-slim as deps
COPY requirements.txt .
RUN pip wheel --no-cache-dir --no-deps --wheel-dir /wheels -r requirements.txt

# Stage 2: Runtime
FROM python:3.11-slim
COPY --from=deps /wheels /wheels
RUN pip install --no-cache /wheels/*
```

### Health Check Customizado
```dockerfile
COPY healthcheck.py /app/
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python healthcheck.py
```

### Secrets Management
```bash
# Usando Docker secrets
echo "my_secret_password" | docker secret create db_password -

# No docker-compose.yml
secrets:
  db_password:
    external: true
```

## 📊 Monitoramento

### Prometheus Metrics
```dockerfile
# Adicionar ao Dockerfile
RUN pip install prometheus-client

# Expor porta de métricas
EXPOSE 9090
```

### Logging Configuration
```yaml
# docker-compose.yml
logging:
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
```

## 🔒 Segurança

### Boas Práticas
```dockerfile
# Usar usuário não-root
RUN addgroup --system appgroup && adduser --system --group appuser
USER appuser

# Minimizar superfície de ataque
RUN apt-get remove -y gcc g++ && apt-get autoremove -y

# Usar imagens oficiais e atualizadas
FROM python:3.11-slim

# Não incluir secrets na imagem
# Use environment variables ou Docker secrets
```

### Scanning de Vulnerabilidades
```bash
# Trivy
trivy image audit-summary-ms

# Docker Scout
docker scout cves audit-summary-ms

# Snyk
snyk container test audit-summary-ms
```

## 🚀 Deploy em Produção

### AWS ECS
```json
{
  "family": "audit-summary-ms",
  "taskRoleArn": "arn:aws:iam::account:role/ecsTaskRole",
  "containerDefinitions": [
    {
      "name": "audit-summary-api",
      "image": "your-registry/audit-summary-ms:latest",
      "memory": 512,
      "cpu": 256,
      "portMappings": [
        {
          "containerPort": 8000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "ENVIRONMENT",
          "value": "production"
        }
      ],
      "secrets": [
        {
          "name": "DB_PASSWORD",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:db-password"
        }
      ]
    }
  ]
}
```

### Kubernetes
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: audit-summary-ms
spec:
  replicas: 3
  selector:
    matchLabels:
      app: audit-summary-ms
  template:
    metadata:
      labels:
        app: audit-summary-ms
    spec:
      containers:
      - name: api
        image: audit-summary-ms:latest
        ports:
        - containerPort: 8000
        env:
        - name: ENVIRONMENT
          value: "production"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
```

## 🔍 Troubleshooting

### Problemas Comuns
```bash
# Container não inicia
docker logs container_name

# Problemas de rede
docker network ls
docker network inspect bridge

# Problemas de volume
docker volume ls
docker volume inspect volume_name

# Problemas de permissão
docker exec -it container_name ls -la /app
```

---

**Versão**: 1.0 | **Data**: 2025-01-20
