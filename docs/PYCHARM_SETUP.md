# 🔧 PyCharm Setup Guide - Audit Summary Microservice

## 📋 Visão Geral

Este guia fornece instruções detalhadas para configurar o PyCharm para desenvolvimento do **Audit Summary Microservice**, um serviço FastAPI que processa e gera resumos de auditorias usando IA.

## 🏗️ Contexto do Projeto

### Stack Tecnológica
- **Framework**: FastAPI + Uvicorn
- **Python**: 3.9+ (recomendado 3.11)
- **Database**: MySQL (AWS RDS)
- **IA/ML**: OpenAI GPT, Anthropic Claude, AWS Bedrock
- **Cloud**: AWS (RDS, Secrets Manager, Lambda via Mangum)
- **Containerização**: Docker

### Arquitetura
```
audit-summary-ms/
├── app.py                 # Entry point FastAPI + Lambda handler
├── api/handlers/          # API route handlers
├── core/                  # Core configurations and models
├── services/              # Business logic layer
├── repositories/          # Data access layer
├── routes/                # Legacy routes (sendo migrado)
└── Auditoria_*.ipynb     # Jupyter notebooks para análise
```

## 🚀 Configuração Inicial

### 1. Abrir Projeto no PyCharm

1. **File** → **Open** → Selecionar pasta `audit-summary-ms`
2. Aguardar indexação completa do projeto
3. Verificar se PyCharm detectou automaticamente como projeto Python

### 2. Configurar Interpretador Python

#### Opção A: Virtual Environment (Recomendado)
```bash
# No terminal do PyCharm
python -m venv venv
source venv/bin/activate  # Linux/Mac
# ou
venv\Scripts\activate     # Windows
```

#### Opção B: Usar venv existente
- **File** → **Settings** → **Project** → **Python Interpreter**
- **Add Interpreter** → **Existing Environment**
- Selecionar: `./venv/bin/python` (ou `./venv/Scripts/python.exe` no Windows)

⚠️ **ATENÇÃO**: O projeto já possui um `venv/` configurado com Python 3.9.13

### 3. Instalar Dependências

```bash
# Dependências principais (inferidas do código)
pip install fastapi uvicorn pydantic
pip install pymysql mysql-connector-python
pip install openai anthropic litellm boto3
pip install mangum  # Para AWS Lambda
pip install jupyter papermill nbconvert  # Para notebooks
pip install tqdm pandas numpy
```

## ⚙️ Configuração de Environment Variables

### 1. Criar arquivo `.env` na raiz
```bash
# Database Configuration
DB_HOST=audta-ai-instance.cdkyomc4a6x4.us-east-1.rds.amazonaws.com
DB_USER=devdb
DB_PASSWORD=your_password_here
DB_LANDZONE=audit_landzone
DB_GOLD=audit_gold

# AI Services
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
DEEPSEEK_API_KEY=your_deepseek_key
MODEL_NAME=claude-3-5-sonnet-20241022

# AWS Configuration
AWS_ACCESS_KEY_ID=your_aws_key
AWS_SECRET_ACCESS_KEY=your_aws_secret
AWS_REGION_NAME=us-east-1
AWS_SECRET_NAME=prod/audie

# Application
ENVIRONMENT=development
LOG_LEVEL=INFO
```

### 2. Configurar no PyCharm
- **Run** → **Edit Configurations**
- Selecionar configuração do FastAPI
- **Environment Variables** → Adicionar variáveis do `.env`

## 📁 Source Roots Configuration

### Configurar Source Roots
1. **File** → **Settings** → **Project** → **Project Structure**
2. Marcar como **Source Folders**:
   - Pasta raiz do projeto
   - `api/`
   - `core/`
   - `services/`
   - `repositories/`

⚠️ **IMPORTANTE**: Isso resolve imports relativos como `from src.api.handlers...`

## 🏃‍♂️ Run/Debug Configurations

### 1. FastAPI Development Server
```
Name: FastAPI Dev Server
Script path: app.py
Parameters: --reload --host 0.0.0.0 --port 8000
Working directory: /path/to/audit-summary-ms
Environment variables: (configuradas acima)
```

### 2. Jupyter Notebook
```
Name: Jupyter Notebook
Script path: jupyter
Parameters: notebook --ip=0.0.0.0 --port=8888 --no-browser
Working directory: /path/to/audit-summary-ms
```

### 3. Docker Container
```
Name: Docker Run
Command: docker run -p 8000:8000 audit-summary-ms
```

## 🗂️ Estrutura de Pastas Detalhada

```
audit-summary-ms/
├── 📁 api/
│   └── handlers/
│       └── summary_handler.py    # FastAPI routes
├── 📁 core/
│   ├── config/
│   │   └── database.py          # DB configuration
│   └── models/
│       └── summary_models.py    # Pydantic models
├── 📁 services/
│   └── summary_service.py       # Business logic
├── 📁 repositories/
│   └── user_repository.py       # Data access + AI integration
├── 📁 routes/                   # ⚠️ Legacy - sendo migrado
│   └── routes.py
├── 📁 controllers/              # ⚠️ Vazio - pode ser removido
├── 📁 types/                    # Type definitions
├── 📁 venv/                     # Virtual environment
├── 📁 __pycache__/             # Python cache
├── app.py                       # Main FastAPI app + Lambda handler
├── Dockerfile                   # Container configuration
├── Auditoria_*.ipynb          # Jupyter notebooks
└── pyvenv.cfg                  # Virtual env config
```

## 🔍 Dependências e Imports

### Principais Dependências
```python
# Web Framework
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
mangum>=0.17.0  # AWS Lambda adapter

# Database
pymysql>=1.1.0
mysql-connector-python>=8.2.0

# AI/ML
openai>=1.3.0
anthropic>=0.7.0
litellm>=1.0.0
boto3>=1.34.0

# Data Processing
pandas>=2.0.0
numpy>=1.24.0
pydantic>=2.0.0

# Development
jupyter>=1.0.0
papermill>=2.4.0
nbconvert>=7.0.0
tqdm>=4.66.0
```

## ⚠️ Pontos de Atenção Críticos

### 1. **Credenciais Hardcoded**
```python
# ❌ PROBLEMA: Credenciais expostas no código
password="jtNr6=LdR+R6aF4-d~J"
aws_secret_access_key="Ks3MM/R+FLxEe+kCLkRw4CEszeCO6T+Km1ZNOH+P"
```
**Solução**: Migrar para variáveis de ambiente

### 2. **Imports Inconsistentes**
```python
# Mistura de imports absolutos e relativos
from src.api.handlers.summary_handler import router
from ...core.models.summary_models import SummaryRequest
```

### 3. **Duplicação de Código**
- `routes/routes.py` e `api/handlers/summary_handler.py` têm lógica similar
- Modelos duplicados entre arquivos

### 4. **Estrutura de Pastas**
- Pasta `controllers/` está vazia
- `src/` prefix inconsistente nos imports

## 🧪 Testing Configuration

### Configurar pytest
```bash
pip install pytest pytest-asyncio httpx
```

### Test Configuration no PyCharm
```
Name: pytest
Target: Custom
Additional Arguments: -v --tb=short
Working directory: /path/to/audit-summary-ms
```

## 🔧 Code Style & Linting

### Configurar no PyCharm
1. **File** → **Settings** → **Editor** → **Code Style** → **Python**
2. **Line length**: 88 (Black standard)
3. **Use tabs**: Desabilitado
4. **Tab size**: 4

### Instalar ferramentas
```bash
pip install black isort flake8 mypy
```

## 🚀 Quick Start Commands

```bash
# Ativar ambiente virtual
source venv/bin/activate

# Instalar dependências
pip install -r requirements.txt  # Criar este arquivo

# Executar aplicação
python app.py

# Executar com uvicorn
uvicorn app:app --reload --host 0.0.0.0 --port 8000

# Executar notebook
jupyter notebook Auditoria_criacao_resumos.ipynb

# Build Docker
docker build -t audit-summary-ms .

# Run Docker
docker run -p 8000:8000 audit-summary-ms
```

## 📚 Recursos Adicionais

- **FastAPI Docs**: http://localhost:8000/docs
- **Swagger UI**: http://localhost:8000/redoc
- **Jupyter**: http://localhost:8888

---

**Autor**: Documentação gerada para desenvolvimento enterprise  
**Versão**: 1.0  
**Data**: 2025-01-20
