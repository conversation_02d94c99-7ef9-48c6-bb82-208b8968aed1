from typing import List, Optional
import unicodedata
from pydantic import BaseModel
import pymysql
from collections import defaultdict
from pymysql import <PERSON><PERSON>r
import random
import os
from functools import wraps
import time
import logging
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import json
from repositories.openai_repository import OpenAIRepository


logger = logging.getLogger(__name__)

def measure_time(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        duration = end_time - start_time
        logger.info(f"Query {func.__name__} executada em {duration:.4f} segundos")
        return result
    return wrapper


class UserRepository:
    def __init__(self, connection, openai_repository: OpenAIRepository = None):
        self.connection = connection
        self.logger = logging.getLogger(__name__)
        self.openai_repository = openai_repository or OpenAIRepository()
    def close_conn(self):
        self.connection.close()
        return        
                
    @measure_time
    def get_conversation_ids(self,fk_id_platform: int, criterion_result: str, criterion_label: str, 
                      start_date: str, end_date: str, attribute_name: str = None, 
                      attribute_values: List[str] = None, limit: int = 100) -> List[str]:

        try:
            # Query base com CTE
            base_query = """
            WITH filtered_criteria AS (
                SELECT
                    ac.external_id
                FROM audit_landzone.audit_chats AS ac
                INNER JOIN audit_processing.audit_criterion_results AS acr
                    ON acr.fk_id_chat = ac.id_chat
                INNER JOIN audit_landzone.audit_criterion AS ac2
                    ON ac2.id_criterion = acr.fk_id_criterion
                LEFT JOIN audit_landzone.audit_rules_parameters AS arp
                    ON arp.id_parameter = acr.fk_id_parameter
                LEFT JOIN audit_landzone.audit_direction AS ad
                    ON ac2.fk_id_direction = ad.id_direction
                {attribute_join}
                WHERE
                    ac.fk_id_platform = %s
                    AND acr.value = %s
                    AND ac.date_chat BETWEEN %s AND %s
                    AND CASE
                        WHEN ac2.id_criterion IN (4, 5, 30) THEN
                            CONCAT(
                                COALESCE(
                                    REPLACE(ac2.label, '{parametro}', arp.value),
                                    ac2.label
                                ),
                                ' pelo ',
                                ad.value
                            )
                        ELSE
                            COALESCE(REPLACE(ac2.label, '{parametro}', arp.value), ac2.label)
                    END = %s
                    {attribute_where}
            )
            SELECT external_id FROM filtered_criteria
            LIMIT %s
            """
            
            # Inicializa a lista de parâmetros para a query
            params = [fk_id_platform, criterion_result, start_date, end_date + " 23:59:59", criterion_label]
            
            # Configurações condicionais com base na presença de attribute_name e attribute_values
            if attribute_name is not None and attribute_values and len(attribute_values) > 0:
                # Adiciona o JOIN para a tabela de atributos
                attribute_join = """
                JOIN audit_landzone.audit_chat_attributes aaa
                    ON aaa.fk_id_chat = ac.id_chat
                """
                
                # Constrói a parte IN da cláusula WHERE
                if len(attribute_values) == 1:
                    # Caso simples com apenas um valor
                    attribute_where = """
                    AND aaa.attribute_name = %s
                    AND aaa.value = %s
                    """
                    params.append(attribute_name)
                    params.append(attribute_values[0])
                else:
                    # Caso com múltiplos valores usando IN
                    placeholders = ', '.join(['%s'] * len(attribute_values))
                    attribute_where = f"""
                    AND aaa.attribute_name = %s
                    AND aaa.value IN ({placeholders})
                    """
                    params.append(attribute_name)
                    params.extend(attribute_values)
            else:
                # Caso não haja atributos, não adiciona nada
                attribute_join = ""
                attribute_where = ""
            
            params.append(limit)
            
            # Monta a query final substituindo os placeholders
            query = base_query.replace( "{attribute_join}", attribute_join).replace("{attribute_where}",attribute_where)
            # Executa a query
            cursor = self.connection.cursor()
            cursor.execute(query, params)
            result = cursor.fetchall()
            ids = [r['external_id'] for r in result]
            return ids
            
        except Error as e:
            self.logger.error(f"Erro ao buscar IDs das conversas: {e}")
            return []
        finally:
            if 'cursor' in locals() and cursor:
                cursor.close()

    @measure_time
    def get_all_criterion_results(self, fk_id_platform: int, start_date: str, end_date: str, attribute_name = None, attribute_value = None) -> List[dict]:
        """
        Recupera todos os resultados de critérios para uma plataforma em um período específico.
        
        Args:
            fk_id_platform: ID da plataforma
            start_date: Data inicial no formato string
            end_date: Data final no formato string
            attribute_name: Nome do atributo (opcional)
            attribute_value: Valor do atributo (opcional)
            
        Returns:
            Lista de dicionários contendo os resultados dos critérios
        """
        try:
            cursor = self.connection.cursor()  # Retorna resultados como dicionários
            
            if attribute_name is None and attribute_value is None:
                query = """
                SELECT t.criterion_label, t.total_audit, t.total_valid, t.total_passed, 
                    t.total_failed, t.score_percentage, t.date
                FROM audit_gold.total_consolid_criterion t
                WHERE id_platform = %s AND date BETWEEN %s AND %s
                ORDER BY date, criterion_label
                """
                params = (fk_id_platform, start_date, end_date + ' 23:59:59')
            else:
                placeholder = ', '.join(['%s'] * len(attribute_value))
                query = f"""
                SELECT t.criterion_label, t.total_audit, t.total_valid, t.total_passed, 
                    t.total_failed, t.score_percentage, t.date
                FROM audit_gold.total_consolid_criterion_attributes t 
                WHERE id_platform = %s AND date BETWEEN %s AND %s 
                AND attribute_name = %s AND attribute_value IN ({placeholder})
                ORDER BY date, criterion_label
                """
                
                params = (fk_id_platform, start_date, end_date + ' 23:59:59', attribute_name, *attribute_value)
  
                
            cursor.execute(query, params)
            result = cursor.fetchall()
            return result
        except Error as e:
            self.logger.error(f"Erro ao buscar resultados de critério: {e}")
            return []
        finally:
            if 'cursor' in locals() and cursor:
                cursor.close()
    @measure_time
    def get_conversations_by_ids(self, external_ids: List[str], fk_id_platform: int) -> List[dict]:
        """
        Recupera as mensagens das conversas com base em uma lista de IDs externos.
        
        Args:
            external_ids: Lista de IDs externos das conversas
            fk_id_platform: ID da plataforma
            
        Returns:
            Lista de dicionários contendo as mensagens das conversas
        """
        try:
            if not external_ids:
                return []
                
            # Cria os placeholders para a cláusula IN
            placeholders = ', '.join(['%s'] * len(external_ids))
            
            # Query para buscar as mensagens
            query = """
            SELECT
                acm.body,
                acm.direction,
                ac.external_id,
                ac.date_chat
            FROM audit_landzone.audit_chats AS ac
            INNER JOIN audit_landzone.audit_chat_messages AS acm
                ON ac.id_chat = acm.fk_id_chat
            WHERE
                ac.external_id IN ({placeholders})
                AND ac.fk_id_platform = %s
            ORDER BY ac.external_id, acm.id_message
            """.format(placeholders=placeholders)
            
            # Prepara os parâmetros da query
            params = external_ids + [fk_id_platform]
            
            # Executa a query
            cursor = self.connection.cursor()
            cursor.execute(query, params)
            result = cursor.fetchall()
            return result
            
        except Error as e:
            self.logger.error(f"Erro ao buscar mensagens das conversas: {e}")
            return []
        finally:
            if 'cursor' in locals() and cursor:
                cursor.close()

    @measure_time
    def get_conversation(self,fk_id_platform: int, criterion_result: str, criterion_label: str, 
                      start_date: str, end_date: str, attribute_name: str = None, 
                      attribute_values: List[str] = None, limit: int = 100) -> List[dict]:
        """
        Recupera mensagens de chat com base em critérios específicos, 
        opcionalmente filtrando por um atributo com múltiplos valores possíveis.
        
        Args:
            fk_id_platform: ID da plataforma
            criterion_result: Resultado do critério ('sim', 'não', etc)
            criterion_label: Rótulo do critério
            start_date: Data inicial do período (formato 'YYYY-MM-DD')
            end_date: Data final do período (formato 'YYYY-MM-DD')
            attribute_name: Nome do atributo para filtrar (opcional)
            attribute_values: Lista de valores possíveis para o atributo (opcional)
            limit: Limite de registros retornados (padrão: 5000)
            
        Returns:
            Lista de dicionários contendo as mensagens de chat filtradas
        """
        try:
            print(attribute_name, attribute_values, limit)
            ids = self.get_conversation_ids(fk_id_platform, criterion_result, criterion_label, 
                        start_date , end_date , attribute_name, attribute_values, limit)
            conversations = self.get_conversations_by_ids(ids,fk_id_platform) 
            return conversations
        except Exception as e :
            print(e)
            return [] 

    @measure_time
    def get_criterion_results(self, fk_id_platform: int, id_criterion: int, start_date: str, end_date: str, attribute_name = None, attribute_value = None) -> List[dict]:
        """
        Recupera resultados para um critério específico em uma plataforma e período.
        
        Args:
            fk_id_platform: ID da plataforma
            id_criterion: ID do critério
            start_date: Data inicial no formato string
            end_date: Data final no formato string
            attribute_name: Nome do atributo (opcional)
            attribute_value: Valor do atributo (opcional)
            
        Returns:
            Lista de dicionários contendo os resultados do critério específico
        """
        try:
            cursor = self.connection.cursor()  # Retorna resultados como dicionários
            
            if attribute_name is None and attribute_value is None:
                query = """
                SELECT t.criterion_label, t.total_audit, t.total_valid, t.total_passed, 
                    t.total_failed, t.score_percentage, t.date
                FROM audit_gold.total_consolid_criterion t
                WHERE id_platform = %s AND criterion_label = %s AND date BETWEEN %s AND %s
                ORDER BY date
                """
                params = (fk_id_platform, id_criterion, start_date, end_date + ' 23:59:59')
            else:
                query = """
                SELECT t.criterion_label, t.total_audit, t.total_valid, t.total_passed, 
                    t.total_failed, t.score_percentage, t.date
                FROM audit_gold.total_consolid_criterion_attributes t 
                WHERE id_platform = %s AND criterion_label = %s AND date BETWEEN %s AND %s 
                AND attribute_name = %s AND attribute_value IN ({placeholder})
                ORDER BY date
                """
                placeholders = ', '.join(['%s'] * len(attribute_value))
                query = query.replace("{placeholder}", placeholders)
                print(query)
                params = [fk_id_platform, id_criterion, start_date, end_date + ' 23:59:59', attribute_name]
                params.extend(attribute_value)
            cursor.execute(query, params)
            result = cursor.fetchall()
            print(result)
            return result
        except Error as e:
            self.logger.error(f"Erro ao buscar resultados de critério: {e}")
            return []
        finally:
            if 'cursor' in locals() and cursor:
                cursor.close()
    
    @measure_time
    def get_criterion_rules(self, criterion_label: str, platform: int, start_date, end_date) -> List[dict]:
        query = """SELECT COALESCE(REPLACE(ar.prompt, '{parametro}', arp.value), ar.prompt) as prompt FROM audit_action_criterion aa
JOIN audit_landzone.audit_criterion ac on aa.fk_id_criterion = ac.id_criterion
JOIN audit_landzone.audit_direction ad on ac.fk_id_direction = ad.id_direction
JOIN audit_landzone.audit_rules ar on aa.id_action_criterion = ar.fk_id_action_criterion
LEFT JOIN audit_landzone.audit_rules_parameters arp on ar.id_rule = arp.fk_id_rule
WHERE aa.fk_id_action = %s AND (
    CASE
      WHEN ac.id_criterion IN (4, 5, 30) THEN
        CONCAT(
          COALESCE(REPLACE(ac.label, '{parametro}', arp.value), ac.label),
          ' pelo ',
          ad.value
        )
      ELSE
        COALESCE(REPLACE(ac.label, '{parametro}', arp.value), ac.label)
    END
  ) = %s"""
        try:
            cursor = self.connection.cursor()
            cursor.execute(query, (platform, criterion_label))
            result = cursor.fetchall()
            cursor.close()
            return result
        except Error as e:
            print(f"Erro ao buscar regras de critério: {e}")
            cursor.close()
            return []

    def get_criterion_params(self,criterion_label ,platform):
        query = """SELECT arp.value FROM audit_action_criterion aa
JOIN audit_landzone.audit_criterion ac on aa.fk_id_criterion = ac.id_criterion
JOIN audit_landzone.audit_direction ad on ac.fk_id_direction = ad.id_direction
JOIN audit_landzone.audit_rules ar on aa.id_action_criterion = ar.fk_id_action_criterion
JOIN audit_landzone.audit_rules_parameters arp on ar.id_rule = arp.fk_id_rule
WHERE aa.fk_id_action = %s AND (
    CASE
      WHEN ac.id_criterion IN (4, 5, 30) THEN
        CONCAT(
          COALESCE(REPLACE(ac.label, '{parametro}', arp.value), ac.label),
          ' pelo ',
          ad.value
        )
      ELSE
        COALESCE(REPLACE(ac.label, '{parametro}', arp.value), ac.label)
    END
  ) = %s;
        """
        try:
            cursor = self.connection.cursor()
            cursor.execute(query, (platform, criterion_label))
            result = cursor.fetchall()
            cursor.close()
            return result
        except Error as e:
            print(f"Erro ao buscar regras de critério: {e}")
            cursor.close()
            return []
    @measure_time
    def get_criterion_description(self, criterion_label: str, platform: int) -> List[dict]:
        query = """
        SELECT criterion_description, criterion_direction
FROM audit_gold.total_consolid_criterion
WHERE criterion_label = %s AND id_platform = %s ORDER BY criterion_description DESC  LIMIT 1
        """
        try:
            cursor = self.connection.cursor()
            cursor.execute(query, (criterion_label, platform))
            result = cursor.fetchone()
            cursor.close()
            return result
        except Error as e:
            print(f"Erro ao buscar descrição de critério: {e}")
            cursor.close()
            return ""

    @measure_time
    def get_tmr_results(self, fk_id_platform: int, start_date: str, end_date: str, attribute_name) -> List[dict]:
        query = """
       SELECT aca.value FROM audit_landzone.audit_chat_attributes aca
JOIN audit_landzone.audit_action aa on aa.id_action = aca.fk_id_action
JOIN audit_landzone.audit_chats ac on ac.id_chat = aca.fk_id_chat
WHERE aa.fk_id_platform = %s AND aca.attribute_name = %s AND ac.date_chat between %s AND %s
        """
        try:
            cursor = self.connection.cursor()
            cursor.execute(query, (fk_id_platform, attribute_name, start_date, end_date + ' 23:59:59'))
            result = cursor.fetchall()
            cursor.close()
            return result
        except Error as e:
            print(f"Erro ao buscar resultados de critério: {e}")
            cursor.close()
            return []

    def process_criterion_results(self,criterion_results_raw:List[dict]) -> str:
        aggregated_results = defaultdict(lambda: {"total": 0, "total_valid": 0, "total_passed": 0, "total_negative": 0})
        # Agrega os valores para critérios repetidos
        for result_dict in criterion_results_raw: # Itera sobre a lista, pegando cada dicionário
            # Acessa os valores do dicionário usando as chaves corretas
            criterion_name = result_dict['criterion_label']
            # Cuidado: a chave no dicionário é 'total_audit', mas você usou 'total' na agregação
            total_audit = result_dict['total_audit']
            total_valid = result_dict['total_valid']
            total_passed = result_dict['total_passed']
            total_failed = result_dict['total_failed']

            aggregated_results[criterion_name]["total"] += total_audit
            aggregated_results[criterion_name]["total_valid"] += total_valid
            aggregated_results[criterion_name]["total_passed"] += total_passed
            aggregated_results[criterion_name]["total_negative"] += total_failed

        results = []

        for criterion_name, values in aggregated_results.items():
            total_valid = values["total_valid"]
            total_passed = values["total_passed"]
            total_negative = values["total_negative"]

            if total_valid > 0:
                percentage_during_filter = round((total_passed / total_valid) * 100, 2)
            else:
                percentage_during_filter = 0.0

            result = (f"Criterio: {criterion_name} "
                    f"Total Valido: {total_valid} "
                    f"Positivos: {total_passed} "
                    f"Negativos: {total_negative} "
                    f"Porcentagem: {percentage_during_filter}%")
            
            results.append(result)

        return "\n".join(results)
    
    def process_rules(self, criterion_rules: List[dict]) -> str:
        rules = [rule['prompt'] for rule in criterion_rules]
        return "\n".join(rules)

    def get_examples(self, example_array):
        id_example_array = [example['external_id'] for example in example_array]
        id_example_array = list(set(id_example_array))
        print(len(id_example_array))
        random.shuffle(id_example_array)
        if len(id_example_array) > 5:
            ids_examples = random.sample(id_example_array, 5)
        else: 
            ids_examples = id_example_array    
        return ids_examples

    def process_time_criterion_results(self, fk_id_platform, start_date, end_date, criterion_name) -> str:
        attribute_name = ""
        results = ""
        if "ia:" in criterion_name.lower() and "segundos" in criterion_name.lower():
            attribute_name = "avg_tmr_agent_ia_seconds"
        elif "agente:" in criterion_name.lower() and "minutos" in criterion_name.lower():    
            attribute_name = "avg_tmr_agent_humano_minutos"
        elif "cliente:" in criterion_name.lower() and "minutos" in criterion_name.lower():
            attribute_name = "avg_tmr_client_minutos"
        elif "agente:" in criterion_name.lower() and "segundos" in criterion_name.lower():
            attribute_name = "avg_tmr_agent_humano_seconds"
        elif "cliente:" in criterion_name.lower() and "segundos" in criterion_name.lower():
            attribute_name = "avg_tmr_client_seconds"
        elif "espera" in criterion_name.lower() and "minutos" in criterion_name.lower():
            attribute_name = "tempo_espera_minutos"
        elif "atendimento" in criterion_name.lower() and "horas" in criterion_name.lower():
            attribute_name = "tempo_atendimento_horas"
        elif "atendimento" in criterion_name.lower() and "segundos" in criterion_name.lower():
            attribute_name = "tempo_atendimento_seconds"
        elif "atendimento" in criterion_name.lower() and "minutos" in criterion_name.lower():
            attribute_name = "tempo_atendimento_minutos"
        elif "espera" in criterion_name.lower() and "segundos" in criterion_name.lower():
            attribute_name = "tempo_espera_seconds"
        print(attribute_name)
        tmr_results_raw = self.get_tmr_results(fk_id_platform, start_date, end_date, attribute_name)
        tmr_results_array = [float(x['value']) for x in tmr_results_raw]
        
        if len(tmr_results_array) > 0:
            mean = round(((sum(tmr_results_array))/ len(tmr_results_array)), 3)
        
        else:
            mean = 0
        mean = int(mean)
        if "minutos" in attribute_name:
            results = str(mean) + " minutos"
        elif "horas" in attribute_name:
            results = str(mean) + " horas"
        else:
            results = str(mean) + " segundos"
        return results    
      
    def formatar_conversas(self, examples, ids_selecionados, criterion_name, criterion_params=False):
        print("#####")
        print(f"Tamanho inicial = {len(examples)}")
        conversas_por_id = {}
        if len(ids_selecionados) == 0:
            return ""
        for exemplo in examples:
            if len( exemplo) >7: 
                mensagem = exemplo['body'] 
                quem_falou = ['exemplo.direction']
                id_conversa = exemplo['external_id']
                horario = exemplo['date_chat'] 
                
            else : 
                mensagem = exemplo['body'] 
                quem_falou = exemplo['direction']
                id_conversa = exemplo['external_id']
                horario = exemplo['date_chat'] 

            if id_conversa in ids_selecionados:
                if id_conversa not in conversas_por_id:
                    conversas_por_id[id_conversa] = []
                
                conversas_por_id[id_conversa].append({
                    "horario": horario,
                    "autor": quem_falou, 
                    "mensagem": mensagem
                })
        
        resultado = []
        criterios_agente = ['Conformidade Linguística','Conformidade Ética', 'Uso de Linguagem Tóxica pelo Agente']
        for id_conversa, mensagens in conversas_por_id.items():
            mensagens.sort(key=lambda x: x["horario"])
            
            conversa_formatada = f"### CONVERSA ID: {id_conversa} ###\n" 
       
            normalized_criterion = self.normalize(criterion_name)
            mencao_rule = self.normalize('Reclamações')
            normalized_names = [self.normalize(c) for c in criterios_agente]

            if mencao_rule in normalized_criterion and criterion_params:
                contem_parametro = False
                for msg in mensagens:
                    for param in criterion_params:
                        if type(msg) == str:
                            if msg['mensagem'] and msg['autor'] == 'Cliente' and param.lower() in msg['mensagem'].lower():
                                contem_parametro = True
                                break
                    if contem_parametro:
                        break
                
                if not contem_parametro:
                    continue
                    
            for msg in mensagens:
                if normalized_criterion in normalized_names:
                    if msg['autor'] == 'Agente':
                        conversa_formatada += f"[{msg['horario']}] {msg['autor']}: {msg['mensagem']}\n"
                else:
                    conversa_formatada += f"[{msg['horario']}] {msg['autor']}: {msg['mensagem']}\n"
                
            conversa_formatada += f"### FIM DA CONVERSA ID: {id_conversa} ###\n\n"
            resultado.append(conversa_formatada)
        print(f"len final {len(resultado)}")
        print("#####")
        return "\n".join(resultado)  
    def process_results(self, criterion_results_raw: List[dict], negative_examples: List[dict], positive_examples: List[dict], params, platform) -> dict:
        try:
            print(criterion_results_raw)
            criterion_name = criterion_results_raw[0].get('criterion_label', '')

            positive_during_filter = 0
            negative_during_filter = 0
            total_during_filter = 0
            for results in criterion_results_raw:
                total_during_filter += results['total_valid'] 
                negative_during_filter += results['total_failed']
                positive_during_filter += results['total_passed']

            if total_during_filter > 0:
                percentage_during_filter = round((positive_during_filter / total_during_filter) * 100, 2)
            else: 
                percentage_during_filter = 0.0
            criterion_result_string = f"Criterio: {criterion_name} Total valido: {total_during_filter} Positivos: {positive_during_filter} Negativos: {negative_during_filter} Porcentagem: {percentage_during_filter}"

            negative_ids = set([example['external_id'] for example in negative_examples])
            positive_ids = set([example['external_id'] for example in positive_examples])
            
            if len(negative_ids) > 100 and len(positive_ids) > 15:
                ids_selecionados_negativos = random.sample(negative_ids, 100)
                ids_selecionados_positivos = random.sample(positive_ids, 15)
            else:
                ids_selecionados_negativos = negative_ids
                ids_selecionados_positivos = positive_ids

            if platform == 19:
                params = ['bacen', 'reclame aqui']
                
            negative_conversation = self.formatar_conversas(negative_examples, ids_selecionados_negativos, criterion_name, params)
            positive_conversation = self.formatar_conversas(positive_examples, ids_selecionados_positivos, criterion_name)
            
            if len(negative_conversation) + len(positive_conversation) > 100_000:
                negative_conversation = negative_conversation[0:90_000]
                positive_conversation = positive_conversation[0:10_000]
            
            print(positive_conversation)
                
            return {
                "criterion_name": criterion_name,
                "criterion_result_string": criterion_result_string,
                "negative_conversation": negative_conversation,
                "positive_conversation": positive_conversation
            }
        except Exception as e:
            print(f"Erro ao processar resultados: {e}")
            raise e

    @measure_time
    def chat_completion_request(self, system, messages: list, model):
        """
        Wrapper method to maintain compatibility with existing code.
        Delegates to OpenAIRepository for actual API calls.
        """
        try:
            # If system message is provided and not empty, add it to messages
            if system and system.strip():
                # Check if first message is already a system message
                if not messages or messages[0].get("role") != "system":
                    messages = [{"role": "system", "content": system}] + messages
                else:
                    # Replace existing system message
                    messages[0]["content"] = system

            response_content, usage = self.openai_repository.chat_completion_request(
                messages=messages,
                model=model,
                temperature=0
            )
            print(f"Response: {response_content}")
            return response_content, usage
        except Exception as e:
            print("Unable to generate ChatCompletion response")
            print(f"Exception: {e}")
            return str(e), None

    def create_message_array_summary(self, prompt, audit_metrics, criterion_name, user, standard_conversation, bad_conversation, model, rules):
        if "claude" not in model:
            if "{parametro}" in rules:
                rules = rules.replace("{parametro}","")
            message_array = []
            message_array.append({"role": "system", "content": prompt.replace("{{nome_criterio}}", criterion_name).replace("{{resultado}}", audit_metrics).replace("{{rules}}", rules)})
            message_array.append({"role": "user", "content": user.format(conversas_boas=standard_conversation, conversas_ruins=bad_conversation)})
            return message_array, ""
        else:
            message_array = []
            system = prompt.replace("{{nome_criterio}}", criterion_name).replace("{{resultado}}", audit_metrics)
            message_array.append({"role": "user", "content": user.format(standard_conversation=standard_conversation, bad_conversation=bad_conversation)})
            return message_array, system

    def create_message_array_summary_all(self, prompt, audit_metrics, user):
        message_array = []
        message_array.append({"role":"system", "content": prompt.replace("{{resultado}}", audit_metrics)})
        message_array.append({"role": "user", "content": user.format(resumo_auditoria=audit_metrics)})
        return message_array, ""
    
    def normalize(self,text):
        return unicodedata.normalize("NFKD", text.strip()).encode("ASCII", "ignore").decode("utf-8").lower()
        
    def generate_summary(self, criterion_name, criterion_result_string, positive_conversation, negative_conversation, rules):
        generator = ThreadedSummaryGenerator(
            criterion_name,
            criterion_result_string,
            positive_conversation,
            negative_conversation,
            rules,
            self.openai_repository
        )
        return generator.generate_summary()

    def generate_time_summary(self, criterion_name, criterion_result_string, positive_conversation, negative_conversation, rules):
        prompt = """Você é um auditor de conversa avaliando o {{nome_criterio}}. Seu objetivo é avaliar o {{nome_criterio}} dos agentes e fornecer um resumo em JSON com base nos resultados obtidos. Por exemplo:
        {"resumo": {"Tempo Atual": "superando o limite de 5 minutos. Esse resultado indica uma oportunidade de melhoria e é importante considerar maneiras de agilizar as respostas dos agentes para proporcionar uma experiência melhor aos clientes."}}, 
        "recomendacoes": {"Treinamento de Atendimento": "Oferecer treinamento para melhorar a eficiência e a qualidade das respostas dos agentes, garantindo que os clientes sejam atendidos de forma rápida e eficaz."}

        Exemplo 2 : 
        {"resumo": {"Tempo Atual": "superando o limite de 2 minutos. Esse resultado indica uma oportunidade de melhoria e é importante considerar maneiras de agilizar as respostas dos agentes para proporcionar uma experiência melhor aos clientes."}}, 
        "recomendacoes": {"Treinamento de Atendimento": "Oferecer treinamento para melhorar a eficiência e a qualidade das respostas dos agentes, garantindo que os clientes sejam atendidos de forma rápida e eficaz."}


        Sinta-se a vontade para adicionar tópicos dentro do JSON indicando as etapas do atendimento onde houve uma demora para responder em um texto corrido. 
        por exemplo : 
        {"resumo": {"tempo atual": "Foi de [X minutos], superando o limite de 5 minutos. Esse resultado indica uma oportunidade de melhoria e é importante considerar maneiras de agilizar as respostas dos agentes para proporcionar uma experiência melhor aos clientes.", "Etapas Cruciais" : "O Agente demorou para responder nas etapas ...  Isso pode ser visto nos IDS <marquee>ID1</marquee>, <marquee>ID2</marquee> e <marquee>ID3</marquee> "}}, 
        "recomendacoes": {"Treinamento de Atendimento": "Oferecer treinamento para melhorar a eficiência e a qualidade das respostas dos agentes, garantindo que os clientes sejam atendidos de forma rápida e eficaz."}

         3.	Inclua 3 IDs específicos das conversas que exemplificam cada ponto fraco (formato <marquee>ID</marquee>). Caso não haja ids suficiente para 3, não repita os ids, use o que tem disponível. Gere o IDs completos por exemplo : <marquee><EMAIL>-2025-06-16</marquee> ou <marquee>52443566</marquee> 

        Dados importantes: 
        O tempo médio dos agentes foi de:  {{resultado}}.
        O tempo limite deveria ser:  {{nome_criterio}}
        """
        user = """
        Conversas não conformes: {conversas_ruins}

        ###

        Conversas conformes : {conversas_boas}
        """
        
        model = os.environ["MODEL_NAME"]
        array_of_messages, system = self.create_message_array_summary(prompt, criterion_result_string, criterion_name, user, positive_conversation, negative_conversation, model, rules)
        response, usage = self.chat_completion_request("", array_of_messages, model)
        return response

    def generate_summary_all(self, criterion_result_string):
        system = """Você é um analista de negócio apresentando resultados de auditoria para o stakeholder. Sua tarefa é criar um "Resumo Inteligente" a partir do texto fornecido, integrando os seguintes aspectos em um único texto corrido:
	1.	Descrição das conversas: O que foi detectado com base nas regras, trazendo tanto casos positivos quanto negativos.
	2.	Critério e Regras: Quais critérios de auditoria e regras específicas foram afetados.

Resultado auditoria: {{abstract}}

Objetivo
	•	Facilitar a rápida compreensão da situação, abordando o que aconteceu, por que é relevante, quais critérios e regras foram afetados e as possíveis implicações.
	•	Se possível, explicar por que algumas situações ocorreram, com base nas informações disponíveis.

Instruções
	1.	Leia atentamente o texto em {abstract} (conteúdo da auditoria).
	2.	Extraia as informações relevantes sobre:
	•	Quais comportamentos foram positivos ("Pontos Fortes").
	•	Quais situações são oportunidades de melhoria ("Oportunidades").
	3.	Explique, quando possível, as causas ou razões por trás dos comportamentos e situações identificadas.
	4.	Apresente o resumo em formato JSON, sem listas ou tópicos externos, e em texto corrido.

Exemplo de Resposta em JSON (mantendo a estrutura, mas adaptando o conteúdo ao seu resumo):

{
  "pontosFortes": [
    {
      "titulo": "Tom respeitoso",
      "descricao": "Os agentes mantiveram um tom respeitoso e livre de linguagem ofensiva, o que é crucial para uma experiência positiva."
    },
    {
      "titulo": "Baixo risco de desistência",
      "descricao": "A maioria das interações não apresentou sinais de intenção de cancelamento, indicando um baixo risco."
    }
  ],
  "oportunidades": [
    {
      "titulo": "Identificação das necessidades",
      "descricao": "A pontuação baixa sugere que os agentes não foram suficientemente proativos em compreender e responder às preocupações dos clientes, impactando a satisfação."
    },
    {
      "titulo": "Satisfação do cliente",
      "descricao": "A avaliação foi considerada baixa, indicando que as soluções apresentadas não atenderam plenamente às expectativas."
    }
  ]
}

Regras:
- Não use nenhum ID
         """
        user = """
       Aqui está as informações para resumir: 
{resumo_auditoria}
        """
        criterion_models = {"Sentimento e Emoção: Satisfação": "us.anthropic.claude-3-5-haiku-20241022-v1:0"}
        model = os.environ["MODEL_NAME"]

        if "claude" in model:
            array_of_messages, system = self.create_message_array_summary_all(system, criterion_result_string , user)
            response, usage = self.chat_completion_request(system, array_of_messages, model)
        else:
            array_of_messages, system = self.create_message_array_summary_all(system, criterion_result_string, user)
            response, usage = self.chat_completion_request("", array_of_messages, model)

        return response

class ThreadedSummaryGenerator:
    def __init__(self, criterion_name, criterion_result_string, positive_conversation, negative_conversation, rules, openai_repository: OpenAIRepository):
        self.criterion_name = criterion_name
        self.criterion_result_string = criterion_result_string
        self.positive_conversation = positive_conversation
        self.negative_conversation = negative_conversation
        self.rules = rules
        self.results = {}
        self.model = 'gpt-4.1-mini'
        self.openai_repository = openai_repository

    def _create_message_array(self, system, user, conversations):
        system = system.replace("{{nome_criterio}}", self.criterion_name)
        system = system.replace("{{resultado}}", self.criterion_result_string)
        system = system.replace("{{rules}}", self.rules)
        
        user = user.format(conversas_ruins=self.negative_conversation, conversas_boas=self.positive_conversation)
        
        return [{"role": "system", "content": system}, {"role": "user", "content": user}]

    def generate_pontos_fracos(self):
        system = """Você é um auditor de conversas de atendimento focado em analisar pontos fracos. 
Analise as conversas fornecidas e identifique os pontos fracos específicos baseando-se nas regras de conformidade.

Critério: {{nome_criterio}}
Resultado: {{resultado}}
Regras: {{rules}}

Instruções:
1. Analise as conversas fornecidas focando apenas nos pontos fracos e não conformidades
2. Baseando-se nas regras fornecidas, identifique padrões de erro e desvios de conformidade
3. Explique especificamente por que cada regra não está conforme
4. Inclua 3 IDs específicos das conversas que exemplificam cada ponto fraco (formato <marquee>ID</marquee>). Caso não haja ids suficiente para 3, não repita os ids, use o que tem disponível. Gere o IDs completos por exemplo : <marquee><EMAIL>-2025-06-16</marquee> ou <marquee>52443566</marquee> 
5. Calcule e mencione percentuais de não conformidade quando aplicável
6. Mantenha o texto em formato corrido, conciso e objetivo
7. Evite termos excessivamente negativos
8. Foque apenas no critério especificado
9. Não use o temo 'Em várias conversas'
Formato de saída obrigatório em JSON:
{
    "⚠️Pontos Fracos": "A regra '[nome da regra]' apresentou falhas nas interações, evidenciadas por [descrição específica dos problemas encontrados], como em <marquee>ID1</marquee>, <marquee>ID2</marquee> e <marquee>ID3</marquee>.[Explicação detalhada do por que a regra não está conforme e padrões identificados]."
}
        """
        user = """
        Conversas: {conversas_ruins}
        """
        
        array_of_messages = self._create_message_array(system, user, self.negative_conversation)
        response, _ = self.openai_repository.chat_completion_request(array_of_messages, self.model)
        return json.loads(response)

    def generate_pontos_fortes(self):
        system = """Você é um auditor de conversas de atendimento focado em analisar pontos fortes. 
Analise as conversas fornecidas e identifique os pontos fortes específicos baseando-se nas regras de conformidade.

Critério: {{nome_criterio}}
Resultado: {{resultado}}
Regras: {{rules}}

Instruções:
1. Analise as conversas fornecidas focando apenas nos pontos fortes e conformidades
2. Identifique padrões de sucesso e boas práticas
3. Explique especificamente por que cada regra está em conformidade
4. Inclua 3 IDs específicos das conversas que exemplificam cada ponto fraco (formato <marquee>ID</marquee>). Caso não haja ids suficiente para 3, não repita os ids, use o que tem disponível. Gere o IDs completos por exemplo : <marquee><EMAIL>-2025-06-16</marquee> ou <marquee>52443566</marquee>
5. Calcule e mencione percentuais de conformidade quando aplicável
6. Mantenha o texto em formato corrido, conciso e objetivo
7. Destaque as práticas exemplares identificadas
8. Foque apenas no critério especificado

Formato de saída obrigatório em JSON:
{
    "✨Pontos Fortes": Os agentes mantiveram  [descrição específica das boas práticas encontradas], como evidenciado em <marquee>ID1</marquee>, <marquee>ID2</marquee> e <marquee>ID3</marquee>. [Explicação detalhada das práticas exemplares e padrões de sucesso identificados]."
}
        """
        user = """Dê sua resposta resumida 
        Conversas: {conversas_boas}
        """
        
        array_of_messages = self._create_message_array(system, user, self.positive_conversation)
        response, _ = self.openai_repository.chat_completion_request(array_of_messages, self.model)
        return json.loads(response)

    def generate_oportunidades(self):
        system = """Você é um auditor de conversas de atendimento focado em identificar oportunidades de melhoria. 
Analise as conversas fornecidas e identifique oportunidades específicas baseando-se nas regras de conformidade.

Critério: {{nome_criterio}}
Resultado: {{resultado}}
Regras: {{rules}}

Instruções:
1. Analise as conversas fornecidas focando em oportunidades de melhoria e otimização
2. Identifique áreas onde pequenas mudanças podem trazer grandes impactos
3. Proponha melhorias específicas e acionáveis para cada oportunidade identificada
4. Inclua 3 IDs específicos das conversas que exemplificam cada ponto fraco (formato <marquee>ID</marquee>). Caso não haja ids suficiente para 3, não repita os ids, use o que tem disponível. Gere o IDs completos por exemplo : <marquee><EMAIL>-2025-06-16</marquee> ou <marquee>52443566</marquee> 
5. Foque em sugestões práticas e implementáveis
6. Mantenha o texto em formato corrido, conciso e objetivo
7. Priorize oportunidades com maior potencial de impacto
8. Foque apenas no critério especificado

Formato de saída obrigatório em JSON:
{
    "⚡Oportunidades": "Há oportunidade para [descrição específica das oportunidades], como observado em <marquee>ID1</marquee>, <marquee>ID2</marquee> e <marquee>ID3</marquee>."
}
        """
        user = """Dê sua resposta resumida em até 1000 caracteres
        Conversas: {conversas_ruins}
        """
        
        array_of_messages = self._create_message_array(system, user, self.negative_conversation)
        response, _ = self.openai_repository.chat_completion_request(array_of_messages, self.model)
        return json.loads(response)

    def generate_recomendacoes(self):
        system = """Você é um auditor de conversas de atendimento focado em gerar recomendações. 
        Com base nas análises anteriores, forneça recomendações específicas.
        
        Critério: {{nome_criterio}}
        Resultado: {{resultado}}
        Regras: {{rules}}
        
        Instruções:
        1. Analise as conversas fornecidas focando em recomendações práticas
        2. Forneça até 3 recomendações específicas e acionáveis
        3. Mantenha o texto conciso e objetivo
        4. Foque apenas no critério especificado
        
        Exemplo de Formato de saída em json:
        {
            "recomendacoes": {
                "Personalizar o atendimento": "Descrição da primeira recomendação",
                "Treinamento": "Descrição da segunda recomendação",
                "Recomendação": "Descrição da terceira recomendação"
            }
        }
        """
        user = """
        Conversas: {conversas_ruins}
        """
        
        array_of_messages = self._create_message_array(system, user, self.negative_conversation)
        response, _ = self.openai_repository.chat_completion_request(array_of_messages, self.model)
        return json.loads(response)

    def generate_summary(self):
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = {
                executor.submit(self.generate_pontos_fracos): "pontos_fracos",
                executor.submit(self.generate_pontos_fortes): "pontos_fortes",
                executor.submit(self.generate_oportunidades): "oportunidades",
                executor.submit(self.generate_recomendacoes): "recomendacoes"
            }

            for future in as_completed(futures):
                result_type = futures[future]
                try:
                    result = future.result()
                    self.results.update(result)
                except Exception as e:
                    print(f"Erro ao gerar {result_type}: {e}")

        return json.dumps({
            "resumo": {
                "⚠️Pontos Fracos": self.results.get("⚠️Pontos Fracos", ""),
                "✨Pontos Fortes": self.results.get("✨Pontos Fortes", ""),
                "⚡Oportunidades": self.results.get("⚡Oportunidades", "")
            },
            "recomendacoes": self.results.get("recomendacoes", {})
        })

    