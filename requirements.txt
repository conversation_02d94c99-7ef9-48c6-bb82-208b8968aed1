# =============================================================================
# 📦 AUDIT SUMMARY MICROSERVICE - DEPENDENCIES
# =============================================================================
# Dependências Python para o Audit Summary Microservice
# Baseado na análise do código existente
# =============================================================================

# =============================================================================
# 🌐 WEB FRAMEWORK
# =============================================================================
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
mangum>=0.17.0  # AWS Lambda ASGI adapter
starlette>=0.27.0

# =============================================================================
# 🗄️ DATABASE
# =============================================================================
pymysql>=1.1.0
mysql-connector-python>=8.2.0
SQLAlchemy>=2.0.0  # Para futuras melhorias
alembic>=1.12.0    # Database migrations

# =============================================================================
# 🤖 AI & MACHINE LEARNING
# =============================================================================
openai>=1.3.0
anthropic>=0.7.0
litellm>=1.0.0
boto3>=1.34.0      # AWS services
botocore>=1.34.0

# =============================================================================
# 📊 DATA PROCESSING
# =============================================================================
pandas>=2.0.0
numpy>=1.24.0
pydantic>=2.0.0
pydantic-settings>=2.0.0

# =============================================================================
# 🔧 UTILITIES
# =============================================================================
python-dotenv>=1.0.0
python-multipart>=0.0.6
typing-extensions>=4.8.0
tqdm>=4.66.0

# =============================================================================
# 📓 JUPYTER & ANALYSIS
# =============================================================================
jupyter>=1.0.0
papermill>=2.4.0
nbconvert>=7.0.0
ipykernel>=6.25.0
matplotlib>=3.7.0
seaborn>=0.12.0

# =============================================================================
# 🔒 SECURITY & AUTH
# =============================================================================
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
cryptography>=41.0.0

# =============================================================================
# 📝 LOGGING & MONITORING
# =============================================================================
structlog>=23.1.0
python-json-logger>=2.0.0
prometheus-client>=0.17.0

# =============================================================================
# 🧪 TESTING (Development)
# =============================================================================
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
httpx>=0.25.0
pytest-mock>=3.11.0
factory-boy>=3.3.0

# =============================================================================
# 🔧 DEVELOPMENT TOOLS
# =============================================================================
black>=23.7.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.5.0
pre-commit>=3.4.0

# =============================================================================
# 🚀 DEPLOYMENT & CONTAINERIZATION
# =============================================================================
gunicorn>=21.2.0
docker>=6.1.0

# =============================================================================
# 📈 PERFORMANCE & CACHING
# =============================================================================
redis>=4.6.0
aioredis>=2.0.0
asyncio-throttle>=1.0.0

# =============================================================================
# 🌍 INTERNATIONALIZATION (Optional)
# =============================================================================
babel>=2.12.0

# =============================================================================
# 📊 ADDITIONAL ANALYSIS TOOLS
# =============================================================================
plotly>=5.15.0
dash>=2.13.0  # Para dashboards interativos
streamlit>=1.25.0  # Para prototipagem rápida

# =============================================================================
# 🔄 ASYNC & CONCURRENCY
# =============================================================================
asyncio>=3.4.3
aiofiles>=23.2.0
httpx>=0.25.0

# =============================================================================
# 📋 VALIDATION & SERIALIZATION
# =============================================================================
marshmallow>=3.20.0
cerberus>=1.3.4

# =============================================================================
# 🎯 SPECIFIC VERSIONS (Pinned for stability)
# =============================================================================
# Versões específicas para garantir compatibilidade
requests==2.31.0
urllib3==2.0.4
certifi==2023.7.22

# =============================================================================
# 📝 NOTES
# =============================================================================
# Para instalar apenas dependências de produção:
# pip install -r requirements.txt --no-dev
#
# Para instalar com dependências de desenvolvimento:
# pip install -r requirements.txt
#
# Para atualizar dependências:
# pip-compile --upgrade requirements.in
#
# Para verificar vulnerabilidades:
# pip-audit
# =============================================================================
