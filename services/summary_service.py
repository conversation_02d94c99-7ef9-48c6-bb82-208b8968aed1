from typing import Dict, Any
import json
from src.core.models.summary_models import SummaryRequest, GeneralSummaryRequest
from src.repositories.user_repository import UserRepository

class SummaryService:
    def __init__(self, user_repo_landzone: UserRepository, user_repo_gold: UserRepository):
        self.user_repo_landzone = user_repo_landzone
        self.user_repo_gold = user_repo_gold

    async def get_summary_by_criterion(self, request: SummaryRequest) -> Dict[str, Any]:
        negative_examples = self.user_repo_landzone.get_conversation(
            request.fk_id_platform, "não", request.criterion_label, 
            request.start_date, request.end_date,
            request.attribute_name, request.attribute_value
        )
        criterion_results_raw = self.user_repo_gold.get_criterion_results(
            request.fk_id_platform, request.criterion_label, 
            request.start_date, request.end_date, 
            request.attribute_name, request.attribute_value
        )
        positive_examples = self.user_repo_landzone.get_conversation(
            request.fk_id_platform, "sim", request.criterion_label, 
            request.start_date, request.end_date, 
            request.attribute_name, request.attribute_value
        )

        if len(negative_examples) == 0 and len(positive_examples) == 0:
            return self._build_response({"data": "Ainda não há interações suficientes para gerar insights"})

        if "tempo" in request.criterion_label.lower():
            return await self._handle_time_criterion(request, negative_examples, positive_examples)

        return await self._handle_regular_criterion(request, negative_examples, positive_examples, criterion_results_raw)

    async def get_general_summary(self, request: GeneralSummaryRequest) -> Dict[str, Any]:
        criterion_results_raw = self.user_repo_gold.get_all_criterion_results(
            request.fk_id_platform, request.start_date, request.end_date,
            request.attribute_name, request.attribute_value
        )
        criterion_string = self.user_repo_landzone.process_criterion_results(criterion_results_raw)
        response = self.user_repo_landzone.generate_summary_all(criterion_string)

        return self._build_response({"data": json.loads(response)})

    def _build_response(self, data: Dict[str, Any]) -> Dict[str, Any]:
        return {
            "statusCode": 200,
            "headers": {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT",
                "Access-Control-Allow-Headers": "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
            },
            "body": json.dumps(data)
        }

    async def _handle_time_criterion(self, request: SummaryRequest, negative_examples: list, positive_examples: list) -> Dict[str, Any]:
        negative_examples_1 = self.user_repo_landzone.get_examples(negative_examples)
        positive_examples_1 = self.user_repo_landzone.get_examples(positive_examples)

        criterion_rules_list = self.user_repo_landzone.get_criterion_rules(
            request.criterion_label, request.fk_id_platform, 
            request.start_date, request.end_date
        )
        processed_rules_str = self.user_repo_landzone.process_rules(criterion_rules_list)
        tmr_mean_str = self.user_repo_landzone.process_time_criterion_results(
            request.fk_id_platform, request.start_date, 
            request.end_date, request.criterion_label
        )
        
        response = self.user_repo_landzone.generate_time_summary(
            request.criterion_label, tmr_mean_str, 
            positive_examples_1, negative_examples_1, 
            processed_rules_str
        )

        return self._build_response({
            "data": json.loads(response),
            "positive_examples": self.user_repo_landzone.get_examples(positive_examples),
            "negative_examples": self.user_repo_landzone.get_examples(negative_examples)
        })

    async def _handle_regular_criterion(self, request: SummaryRequest, negative_examples: list, positive_examples: list, criterion_results_raw: list) -> Dict[str, Any]:
        params = self.user_repo_landzone.get_criterion_params(request.criterion_label, request.fk_id_platform)
        processed_results = self.user_repo_landzone.process_results(
            criterion_results_raw, negative_examples, 
            positive_examples, params, request.fk_id_platform
        )
        
        criterion_rules_list = self.user_repo_landzone.get_criterion_rules(
            request.criterion_label, request.fk_id_platform, 
            request.start_date, request.end_date
        )
        processed_rules_str = self.user_repo_landzone.process_rules(criterion_rules_list)

        if len(criterion_rules_list) > 0:
            response = self.user_repo_landzone.generate_summary(
                processed_results["criterion_name"],
                processed_results['criterion_result_string'],
                processed_results['positive_conversation'],
                processed_results['negative_conversation'],
                processed_rules_str
            )

        return self._build_response({
            "data": json.loads(response),
            "positive_examples": self.user_repo_landzone.get_examples(positive_examples),
            "negative_examples": self.user_repo_landzone.get_examples(negative_examples)
        }) 